import { type CustomFieldRequestDef, type FormattedCustomFieldDef } from '@moego/bff-openapi/clients/client.temp';
import { type FieldValues, Form, useForm, type FormProps } from '@moego/ui';
import React, { forwardRef, useImperativeHandle, useMemo } from 'react';
import { PrefixBasicFormFields } from './BasicComponent/PrefixBasicFormFields';
import { SuffixBasicFormFields } from './BasicComponent/SuffixBasicFormFields';
import { componentForms } from './componentForms/componentForms';

export type CustomFieldFormInstance = ReturnType<typeof useForm<CustomFieldRequestDef>>;
export interface CustomFieldFormRef {
  form: CustomFieldFormInstance;
}

export interface CustomFieldFormProps extends Omit<FormProps<CustomFieldRequestDef>, 'form' | 'defaultValue'> {
  isEdit: boolean;
  defaultValue?: Partial<FormattedCustomFieldDef>;
}

export const CustomFieldForm = forwardRef<CustomFieldFormRef, CustomFieldFormProps>(
  function CustomFieldForm(props, ref) {
    const { isEdit, defaultValue, ...rest } = props;
    const form = useForm<FieldValues>({
      defaultValues: defaultValue,
    }) as unknown as CustomFieldFormInstance;
    useImperativeHandle(ref, () => ({ form }));

    const type = form.watch('type');
    const Comp = useMemo(() => componentForms?.[type as keyof typeof componentForms] ?? (() => <></>), [type]);

    return (
      <Form {...rest} form={form} footer={null}>
        <PrefixBasicFormFields form={form} isEdit={isEdit} />
        <Comp form={form} />
        <SuffixBasicFormFields form={form} />
      </Form>
    );
  },
);
