import { type z } from '@moego/bff-openapi';
import { type schemas } from '@moego/bff-openapi/clients/client.leads';
import { MoreAction } from '@moego/fn-components';
import { renderCountableNounPlurals } from '@moego/reporting';
import { type ColumnDef, Heading, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { useHistory } from 'react-router';
import { toastApi } from '../../../../components/Toast/Toast';
import { LeadsTestIds } from '../../../../config/testIds/leads';
import { BffLeadsClient } from '../../../../middleware/bff';
import { PATH_LEADS_DETAIL } from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { referralSourceMapBox } from '../../../../store/business/referralSource.boxes';
import { staffMapBox } from '../../../../store/staff/staff.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { googleTimestampToDayjs } from '../../../../utils/googleTimestamp';
import { confirmDeleteLead } from '../utils/deleteLead';
import { leadLifeCycleMapBox } from '../../../../store/leads/leadLifeCycle.boxes';
import { leadActionStatusMapBox } from '../../../../store/leads/leadActionStatus.boxes';
import { queryLead } from '../../../../query/lead/lead';
import { CustomField_AssociationType, type CustomFieldValueDef } from '@moego/bff-openapi/clients/client.temp';
import { selectCurrentCompany } from '../../../../store/company/company.selectors';
import { renderCustomField } from '../../../settings/Settings/ClientsAndPetsSetting/CustomFields/util';
import { CompressedAvatar } from '@moego/business-components';
export const useColumns = ({
  setCurrentCustomer,
  openConvertModal,
  refresh,
  deleteRowSelection,
}: {
  setCurrentCustomer: (customer: z.infer<typeof schemas.CustomerWithNameAndTask>) => void;
  openConvertModal: () => void;
  refresh: () => void;
  deleteRowSelection: (id: string) => void;
}) => {
  const [business, currentCompany, staffMap, referralSourceMap, leadLifeCycleMap, leadActionStatusMap] = useSelector(
    selectCurrentBusiness(),
    selectCurrentCompany,
    staffMapBox,
    referralSourceMapBox,
    leadLifeCycleMapBox,
    leadActionStatusMapBox,
  );
  const history = useHistory();

  const { data: customFields } = queryLead.listCustomField.useQuery({
    associationType: CustomField_AssociationType.LEAD,
  });

  const handleConvert = async (lead: z.infer<typeof schemas.CustomerWithNameAndTask>) => {
    setCurrentCustomer?.(lead);
    openConvertModal?.();
  };

  const handleEdit = (lead: z.infer<typeof schemas.CustomerWithNameAndTask>) => {
    history.push(PATH_LEADS_DETAIL.build({ id: lead.id }));
  };

  const handleDelete = async (lead: z.infer<typeof schemas.CustomerWithNameAndTask>) => {
    if (await confirmDeleteLead()) {
      await BffLeadsClient.deleteCustomer({ customerId: lead.id });
      deleteRowSelection(lead.id);
      toastApi.success('Lead deleted successfully');
      refresh();
    }
  };

  const baseColumns: ColumnDef<z.infer<typeof schemas.CustomerWithNameAndTask>>[] = [
    {
      id: 'avatar',
      minSize: 68,
      accessorKey: 'avatar',
      header: '',
      cell: ({ row }) => {
        const { avatarPath, name } = row.original;
        return <CompressedAvatar.Client name={name} src={avatarPath} color="#000000" size="s" />;
      },
      meta: {
        sticky: 'left',
      },
    },
    {
      id: 'name',
      minSize: 137,
      header: 'Name',
      accessorKey: 'name',
      cell: ({ row }) => <Heading size="5">{row.original.name}</Heading>,
      meta: {
        sticky: 'left',
      },
    },
    {
      id: 'phone',
      header: 'Phone number',
      accessorKey: 'phone',
      minSize: 204,
      cell: ({ row }) => {
        const { phoneNumber } = row.original;
        if (!phoneNumber) {
          return null;
        }
        return (
          <Text variant="regular-short" as="div">
            {business.formatPhoneNumber(phoneNumber)}
          </Text>
        );
      },
    },
    {
      id: 'createTime',
      header: 'Created time',
      accessorKey: 'createTime',
      minSize: 201,
      enableSorting: true,
      sortingFn: () => 0,
      cell: ({ row }) =>
        row.original.createTime && (
          <Text variant="regular-short" as="div">
            {business.formatDate(googleTimestampToDayjs(row.original.createTime))}
          </Text>
        ),
    },
    {
      id: 'source',
      header: 'Source',
      accessorKey: 'Source',
      minSize: 177,
      cell: ({ row }) => (
        <Text variant="regular-short">
          {referralSourceMap.mustGetItem(Number(row.original.additionalInfo?.referralSourceId)).sourceName || ''}
        </Text>
      ),
    },
    {
      id: 'lifeCycle',
      header: 'Lifecycle',
      minSize: 177,
      accessorKey: 'lifeCycle',
      cell: ({ row }) => {
        const { customizeLifeCycleId } = row.original;
        return <Text variant="regular-short">{leadLifeCycleMap.mustGetItem(customizeLifeCycleId).name}</Text>;
      },
    },
    {
      id: 'agent',
      header: 'Agent',
      minSize: 177,
      accessorKey: 'agent',
      cell: ({ row }) => {
        const { allocateStaffId } = row.original;
        return (
          <Text variant="regular-short">
            {isNormal(allocateStaffId) ? staffMap.mustGetItem(Number(allocateStaffId)).fullName() : 'No owner'}
          </Text>
        );
      },
    },
    {
      id: 'task',
      header: 'Task',
      size: 136,
      accessorKey: 'task',
      cell: ({ row }) => {
        const { lastTaskName, newTaskLength } = row.original;
        switch (newTaskLength) {
          case 0:
            return '';
          case 1:
            return (
              <Text variant="regular-short" className="moe-w-[120px]" ellipsis>
                {lastTaskName}
              </Text>
            );
          default:
            return <Text variant="regular-short">{renderCountableNounPlurals(newTaskLength, 'task')}</Text>;
        }
      },
    },
    {
      id: 'actionStatus',
      header: 'Action status',
      minSize: 178,
      accessorKey: 'actionStatus',
      cell: ({ row }) => {
        const { customizeActionStateId } = row.original;
        return <Text variant="regular-short">{leadActionStatusMap.mustGetItem(customizeActionStateId).name}</Text>;
      },
    },
    ...(customFields?.map<ColumnDef<z.infer<typeof schemas.CustomerWithNameAndTask>>>((field) => ({
      id: field.id,
      header: field.label,
      accessorKey: field.label,
      minSize: 178,
      cell: ({ row }) => (
        <Text variant="regular-short">
          {renderCustomField(
            field,
            {
              formatDate: currentCompany.formatDate.bind(currentCompany),
              formatDateTime: currentCompany.formatDateTime.bind(currentCompany),
              formatTime: currentCompany.formatTime.bind(currentCompany),
            },
            row.original.customFields as Partial<{ [key: string]: CustomFieldValueDef }>,
          )}
        </Text>
      ),
    })) ?? []),
    {
      id: 'action',
      header: 'Action',
      minSize: 102,
      accessorKey: 'action',
      meta: {
        sticky: 'right',
      },
      cell: ({ row }) => {
        return (
          <MoreAction
            className="moe-gap-[4px]"
            dropdownProps={{
              classNames: {
                content: 'moe-w-[200px]',
              },
            }}
            buttonProps={{
              variant: 'tertiary',
            }}
            iconButtonProps={{
              ['data-testid']: LeadsTestIds.LeadListMoreActionIconButton,
            }}
            options={[
              { key: 'convert', label: 'Convert', onClick: () => handleConvert(row.original) },
              { key: 'edit', label: 'Edit lead', onClick: () => handleEdit(row.original) },
              {
                key: 'delete',
                label: 'Delete lead',
                onClick: () => handleDelete(row.original),
                menuItemProps: { isDestructive: true },
              },
            ]}
          />
        );
      },
    },
  ];
  return baseColumns;
};
