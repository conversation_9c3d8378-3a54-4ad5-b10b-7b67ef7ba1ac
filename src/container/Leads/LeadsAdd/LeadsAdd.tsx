import {
  type JsonObject,
  type Customer,
  type Pet_PetGender,
  type Pet_PetType,
} from '@moego/bff-openapi/clients/client.leads';
import { useSerialCallback } from '@moego/tools';
import { Button, Form, Heading, IconButton, MajorCloseOutlined } from '@moego/ui';
import { type UseFormHandleSubmit, useForm, useFormState } from '@moego/ui';
import { useBoolean, useMemoizedFn, useMount } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import React, { useState } from 'react';
import { useRef } from 'react';
import { useHistory } from 'react-router';
import { useBeforeUnload } from 'react-use';
import { type PetFormRef } from '../../../components/AddClientWithPets/components/PetFormModal/PetForm';
import { type PetFormFields } from '../../../components/AddClientWithPets/components/PetFormModal/usePetForm';
import { generatePetRefsPlaceholder } from '../../../components/AddClientWithPets/hooks/useAddClientWithPetsForm';
import { type AddressFormRef } from '../../../components/AddressForm/AddressFormV2';
import { toastApi } from '../../../components/Toast/Toast';
import { ScrollerProvider } from '../../../layout/components/ScrollerProvider';
import { BffLeadsClient } from '../../../middleware/bff';
import { PATH_LEADS_MANAGEMENT } from '../../../router/paths';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { getReferralSourceList } from '../../../store/business/referralSource.actions';
import { getPetOptions } from '../../../store/pet/pet.actions';
import { getStaffList } from '../../../store/staff/staff.actions';
import { useBizIdReadyEffect } from '../../../utils/hooks/useBizIdReadyEffect';
import { unSavedDoubleConfirmV2 } from '../../../utils/hooks/useUnsavedConfirmV2';
import { isNumeric } from '../../../utils/number';
import { AddLeadFormAnchor } from './components/AddLeadFormAnchor';
import { AddressInfo } from './components/AddressInfo';
import { ExistRemindModal } from './components/ExistRemindModal';
import { LeadInfo } from './components/LeadInfo';
import { PetInfo } from './components/PetInfo';
import { type AddLeadFormBasicFields, type LeadsFields, defaultLeadDetail, dirtyCheckConfig } from './constants';
import { updatePayload } from './utils';
import { getLeadLifeCycleList } from '../../../store/leads/leadLifeCycle.actions';
import { getLeadActionStatusList } from '../../../store/leads/leadActionStatus.actions';
import { type CreateCustomerWithPetRequest } from '@moego/bff-openapi/clients/client.leads';
import { SourceKinds } from '../../../store/customer/customer.boxes';
import { CustomFields } from './components/CustomFields';
import { CustomField_AssociationType, type CustomFieldObject } from '@moego/bff-openapi/clients/client.temp';
import { queryLead } from '../../../query/lead/lead';
export const LeadsAdd = () => {
  const dispatch = useDispatch();
  const history = useHistory();
  const form = useForm<AddLeadFormBasicFields>({
    mode: 'all',
    shouldFocusError: true,
  });
  const [addressVisibleStatus, setAddressVisibleStatus] = useBoolean(false);
  const [existRemindModalVisible, setExistRemindModalVisible] = useBoolean(false);
  const [currentBusinessId] = useSelector(currentBusinessIdBox);
  const customFieldForm = useForm<CustomFieldObject>();
  const { data: customFields } = queryLead.listCustomField.useQuery({
    associationType: CustomField_AssociationType.LEAD,
  });

  const addressRef = useRef<AddressFormRef>(null);
  const [existCustomer, setExistCustomer] = useState<Customer | undefined>(undefined);
  const petRefs = useRef<Record<string, PetFormRef | null>>(generatePetRefsPlaceholder(1));

  const { isDirty } = useFormState({ control: form.control });

  const getIsDirty = useMemoizedFn(() => {
    return (
      isDirty ||
      addressRef.current?.form.formState.isDirty ||
      Object.values(petRefs.current).some((ref) => ref?.form.formState.isDirty) ||
      customFieldForm.formState.isDirty
    );
  });

  const handleClose = () => {
    history.push(PATH_LEADS_MANAGEMENT.build());
  };

  const handleSubmit = useMemoizedFn<UseFormHandleSubmit<LeadsFields>>((onValid, onInvalid) => {
    return async (e?: React.BaseSyntheticEvent) => {
      const petList: PetFormFields[] = [];
      const values: LeadsFields = { petList } as LeadsFields;
      const errors = {};

      const updateErrors = (data: unknown) => Object.assign(errors, data);

      const petListResult = Object.values(petRefs.current)
        .filter(Boolean)
        .map((ref) => {
          const payload = {} as PetFormFields;
          petList.push(payload);
          return ref?.form.handleSubmit(updatePayload(payload), updateErrors)();
        });
      const addressResult = addressVisibleStatus
        ? addressRef.current?.form.handleSubmit(updatePayload(values), updateErrors)()
        : Promise.resolve();
      const leadInfoResult = form.handleSubmit(updatePayload(values), updateErrors)();
      const customFieldResult = customFieldForm.handleSubmit(
        (val) =>
          updatePayload(values)({
            customFields: val,
          }),
        updateErrors,
      )();

      await Promise.all([leadInfoResult, addressResult, ...petListResult, customFieldResult]);
      if (Object.keys(errors).length) {
        await onInvalid?.(errors, e);
      } else {
        await onValid(values, e);
      }
    };
  });

  const handleCancel = useMemoizedFn(async () => {
    if (isDirty) {
      await unSavedDoubleConfirmV2(void 0, handleClose, dirtyCheckConfig);
    } else {
      handleClose();
    }
  });
  const transformToLeadsData = (data: LeadsFields) => {
    const {
      preferredBusinessId,
      phoneNumber,
      type,
      avatarPath = '',
      givenName = '',
      familyName = '',
      email = '',
      customizeLifeCycleId,
      customizeActionStateId,
      source = '',
      allocateStaffId,
      address1,
      address2,
      lat,
      lng,
      petList,
      city,
      state,
      country,
      zipcode,
      customFields,
    } = data;
    const result: CreateCustomerWithPetRequest = {
      customer: {
        preferredBusinessId: preferredBusinessId ? preferredBusinessId : currentBusinessId.toString(),
        phoneNumber,
        type,
        avatarPath,
        givenName: givenName || '*' + phoneNumber.slice(-3),
        familyName: familyName || 'lead',
        email,
        customizeLifeCycleId,
        customizeActionStateId,
        customFields: customFields as JsonObject,
        source: SourceKinds.Manual,
        allocateStaffId: allocateStaffId ? String(allocateStaffId) : undefined,
        ...(source
          ? {
              additionalInfo: {
                referralSourceId: isNumeric(source) ? String(source) : '',
              },
            }
          : {}),
      },
      pets: petList.map((pet) => {
        const { petName, petTypeId, breed, gender, breedMix } = pet;
        return {
          name: petName,
          petType: petTypeId as Pet_PetType,
          breed,
          gender: gender as Pet_PetGender,
          mixed: Boolean(breedMix),
        };
      }),
    };
    if (address1) {
      result.customer.address = {
        address1,
        address2,
        lat,
        lng,
        city,
        state,
        regionCode: country,
        zipcode,
      };
    }
    return result;
  };

  const handleSave = useSerialCallback(async () => {
    const submit = handleSubmit(async (values) => {
      console.log(values);
      try {
        const { customer, isExist } = await BffLeadsClient.createCustomer(transformToLeadsData(values));

        if (isExist && customer) {
          setExistRemindModalVisible.setTrue();
          setExistCustomer(customer);
        } else {
          toastApi.success('New lead added');
          history.push(PATH_LEADS_MANAGEMENT.build());
        }
      } catch (error) {
        console.error(error);
        toastApi.error(`An error occurred while add lead info, please try again latter`);
      }
    });
    await submit();
  });

  useBeforeUnload(getIsDirty, 'You have unsaved changes, are you sure?');

  useBizIdReadyEffect(() => {
    dispatch([
      getPetOptions(),
      getReferralSourceList(),
      getStaffList(),
      getLeadLifeCycleList(),
      getLeadActionStatusList(),
    ]);
  }, []);

  const initForm = async () => {
    form.reset(
      {
        ...defaultLeadDetail,
      },
      { keepDirty: false },
    );
  };

  useMount(() => {
    initForm();
  });

  const renderHeader = () => {
    return (
      <div className="moe-bg-white moe-sticky moe-top-[0px] moe-z-[1] moe-flex moe-h-[64px] moe-w-full moe-items-center moe-justify-between">
        <IconButton icon={<MajorCloseOutlined />} onPress={handleCancel} color="transparent" size="xl" />
        <div className="moe-flex">
          <Button isLoading={handleSave.isBusy()} className="moe-ml-s" size="l" onPress={handleSave}>
            Save
          </Button>
        </div>
      </div>
    );
  };

  const handleExistRemindModalClose = () => {
    setExistRemindModalVisible.setFalse();
    setExistCustomer(undefined);
  };

  return (
    <ScrollerProvider
      style={{
        maxHeight: '100vh',
        padding: 0,
      }}
    >
      <div className="moe-w-full moe-px-6 moe-pb-14 moe-font-manrope" id="moeAddLeadAnchorId">
        {renderHeader()}
        <div className="moe-mt-[32px] moe-flex moe-justify-center [@media(min-width:1450px)]:moe-grid [@media(min-width:1450px)]:moe-grid-cols-12 [@media(min-width:1450px)]:moe-gap-x-[24px]">
          <div className="[@media(max-width:1450px)]:moe-w-[900px] [@media(min-width:1450px)]:moe-col-span-8 [@media(min-width:1450px)]:moe-col-start-3">
            <Heading size="2">Add lead</Heading>
            <div className="moe-relative moe-mt-xl moe-grid moe-grid-cols-8 moe-items-start">
              <div className="moe-col-span-6">
                <div className="moe-flex moe-flex-col moe-gap-y-l">
                  <Form form={form} footer={null}>
                    <LeadInfo />
                    <AddressInfo
                      addressRef={addressRef}
                      setAddressVisibleStatus={setAddressVisibleStatus}
                      addressVisibleStatus={addressVisibleStatus}
                    />
                    <PetInfo petRefs={petRefs} />
                  </Form>
                  <CustomFields form={customFieldForm} fields={customFields} />
                </div>
              </div>
              <div className="moe-sticky moe-top-[176px] moe-col-span-2 moe-ml-xl">
                <AddLeadFormAnchor showCustomFields={!!customFields?.length} />
              </div>
            </div>
          </div>
        </div>
      </div>
      <ExistRemindModal
        isOpen={existRemindModalVisible}
        customer={existCustomer}
        handleClose={handleExistRemindModalClose}
      />
    </ScrollerProvider>
  );
};
