import {
  CustomField_Type,
  type CustomFieldObject,
  type FormattedCustomFieldDef,
} from '@moego/bff-openapi/dist/esm/clients/client.temp';
import { memo, useMemo } from 'react';
import React from 'react';
import { type ItemProps } from '../types';
import { Field } from './Field';
import { useSerialCallback } from '@moego/finance-utils';
import { type JsonObject } from '@moego/bff-openapi/clients/client.leads';

interface LeadCustomFieldsProps extends ItemProps {
  customFields: FormattedCustomFieldDef[];
}

export const LeadCustomFields = memo<LeadCustomFieldsProps>(function LeadCustomFields(props) {
  const { customer, customFields, onChange, refresh } = props;

  const handleChange = useSerialCallback(async (v: CustomFieldObject) => {
    await onChange({
      customFields: v as JsonObject,
    });
    refresh();
  });

  const formattedCustomerValue = useMemo(() => {
    const keys = Object.keys(customer.customFields || {});
    return keys.reduce<CustomFieldObject>((acc, key) => {
      const targetField = customFields.find((f) => f.code === key);

      const valueField = (customer as any)?.customFields?.[key];
      const mode = typeof valueField === 'string' ? 'string' : 'object';

      if (targetField) {
        switch (targetField.type) {
          case CustomField_Type.SHORT_TEXT:
          case CustomField_Type.LONG_TEXT:
            if (mode === 'string') {
              return {
                ...acc,
                [key]: valueField as string,
              };
            } else {
              return {
                ...acc,
                [key]: valueField?.string as string,
              };
            }

          case CustomField_Type.DATE:
          case CustomField_Type.DATETIME:
            if (mode === 'string') {
              return {
                ...acc,
                [key]: valueField as string,
              };
            } else {
              return {
                ...acc,
                [key]: valueField?.timestampTime as string,
              };
            }
        }
      }
      return acc;
    }, {});
  }, [customer.customFields, customFields]);

  return (
    <>
      {customFields.map((field) => (
        <Field
          key={field.code}
          fields={customFields}
          field={field}
          value={formattedCustomerValue}
          onChange={handleChange}
        />
      ))}
    </>
  );
});
