package com.moego.client.api.v1.online_booking.service;

import com.moego.client.api.v1.online_booking.converter.ServiceConverter;
import com.moego.idl.client.online_booking.v1.GetAvailableGroupClassesResult;
import com.moego.idl.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult;
import com.moego.idl.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.TrainerView;
import com.moego.idl.models.fulfillment.v1.GroupClassDetailModel;
import com.moego.idl.models.grooming.v1.ShowBasePrice;
import com.moego.idl.models.offering.v1.GroupClassInstance;
import com.moego.idl.models.offering.v1.GroupClassInstance.Status;
import com.moego.idl.models.offering.v1.GroupClassSession;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeDef;
import com.moego.idl.models.online_booking.v1.BoardingServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceAvailabilityModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.fulfillment.v1.GroupClassDetailServiceGrpc;
import com.moego.idl.service.fulfillment.v1.ListGroupClassDetailsRequest;
import com.moego.idl.service.offering.v1.GroupClassServiceGrpc;
import com.moego.idl.service.offering.v1.ListInstancesRequest;
import com.moego.idl.service.offering.v1.ListServiceRequest;
import com.moego.idl.service.offering.v1.ListSessionsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBoardingServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.GetDaycareServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.ListAvailableBookingTimeRangeRequest;
import com.moego.idl.service.online_booking.v1.ListAvailableBookingTimeRangeResponse;
import com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc;
import com.moego.idl.service.online_booking.v1.QueryAvailableBookingDateRangeRequest;
import com.moego.idl.service.online_booking.v1.QueryAvailableBookingDateRangeResponse;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.server.grooming.api.IOnlineBookingService;
import com.moego.server.grooming.params.ob.ServiceOBSettingQueryParams;
import jakarta.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class AvailabilityService {

    private final OBAvailabilitySettingServiceGrpc.OBAvailabilitySettingServiceBlockingStub availabilitySettingService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub offeringService;
    private final GroupClassServiceGrpc.GroupClassServiceBlockingStub groupClassService;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final IOnlineBookingService onlineBookingService;
    private final GroupClassDetailServiceGrpc.GroupClassDetailServiceBlockingStub groupClassDetailStub;

    public BoardingServiceAvailabilityModel getBoardingLodgingAvailability(Long companyId, Long businessId) {
        return availabilitySettingService
                .getBoardingServiceAvailabilitySetting(GetBoardingServiceAvailabilitySettingRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build())
                .getBoardingServiceAvailabilitySetting();
    }

    public DaycareServiceAvailabilityModel getDaycareLodgingAvailability(Long companyId, Long businessId) {
        return availabilitySettingService
                .getDaycareServiceAvailabilitySetting(GetDaycareServiceAvailabilitySettingRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build())
                .getDaycareServiceAvailabilitySetting();
    }

    @Nullable
    public ArrivalPickUpTimeDef getArrivalPickUpTimeDef(
            Long companyId, Long businessId, ServiceItemType serviceItemType) {
        var response = availabilitySettingService.listAvailableBookingTimeRange(
                ListAvailableBookingTimeRangeRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .addServiceItemType(serviceItemType)
                        .build());

        var result = response.getTimeRangesList().stream()
                .filter(resp -> Objects.equals(serviceItemType, resp.getServiceItemType()))
                .findAny();
        return result.map(ListAvailableBookingTimeRangeResponse.TimeRange::getArrivalPickUpTimeRange)
                .orElse(null);
    }

    public QueryAvailableBookingDateRangeResponse getOBAvailableDateRangeSetting(
            Long companyId, Long businessId, ServiceItemType serviceItemType) {
        return availabilitySettingService.queryAvailableBookingDateRange(
                QueryAvailableBookingDateRangeRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .setServiceItemType(serviceItemType)
                        .build());
    }

    private List<Long> getCompletedGroupClassIds(long petId) {
        return groupClassDetailStub
                .listGroupClassDetails(ListGroupClassDetailsRequest.newBuilder()
                        .setFilter(ListGroupClassDetailsRequest.Filter.newBuilder()
                                .addPetIds(petId)
                                .addStatuses(GroupClassDetailModel.Status.COMPLETED)
                                .build())
                        .build())
                .getGroupClassDetailsList()
                .stream()
                .map(GroupClassDetailModel::getGroupClassId)
                .distinct()
                .toList();
    }

    public GetAvailableGroupClassesResult getAvailableGroupClasses(
            long companyId, long businessId, long petId, PaginationRequest pagination) {
        final var serviceRequest = ListServiceRequest.newBuilder()
                .addBusinessIds(businessId)
                .addServiceItemTypes(ServiceItemType.GROUP_CLASS)
                .setPagination(pagination)
                .setTokenCompanyId(companyId)
                .setInactive(false)
                .setServiceType(ServiceType.SERVICE)
                .addAllPrerequisiteClassIds(getCompletedGroupClassIds(petId))
                .setFilterPrerequisiteClasses(true);

        final var response = offeringService.listService(serviceRequest.build());
        if (CollectionUtils.isEmpty(response.getServicesList())) {
            return GetAvailableGroupClassesResult.newBuilder()
                    .addAllGroupClasses(Collections.emptyList())
                    .setPagination(response.getPagination())
                    .build();
        }

        final var groupClassIds = response.getServicesList().stream()
                .map(ServiceModel::getServiceId)
                .collect(Collectors.toList());

        // filter group class by instance status
        final var instanceRequest = ListInstancesRequest.newBuilder()
                .setPagination(pagination)
                .setCompanyId(companyId)
                .addBusinessIds(businessId)
                .addAllGroupClassIds(groupClassIds)
                .setStatus(Status.UPCOMING)
                .build();
        final var instanceResponse = groupClassService.listInstances(instanceRequest);
        final var availableGroupClassIds = instanceResponse.getGroupClassInstancesList().stream()
                .map(GroupClassInstance::getGroupClassId)
                .collect(Collectors.toSet());

        final var serviceOBSettingMap = onlineBookingService.getServiceOBSetting(new ServiceOBSettingQueryParams(
                companyId,
                businessId,
                availableGroupClassIds.stream().map(Long::intValue).toList()));
        final var serviceOBConfigs = serviceOBSettingMap.entrySet().stream()
                .<GetAvailableGroupClassesResult.OBServiceConfigView>map(
                        entry -> GetAvailableGroupClassesResult.OBServiceConfigView.newBuilder()
                                .setGroupClassId(entry.getKey())
                                .setShowPriceType(
                                        ShowBasePrice.forNumber(entry.getValue().showBasePrice()))
                                .build())
                .collect(Collectors.toList());

        return GetAvailableGroupClassesResult.newBuilder()
                .addAllGroupClasses(response.getServicesList().stream()
                        .filter(service -> availableGroupClassIds.contains(service.getServiceId()))
                        .map(ServiceConverter::toGroupClassModel)
                        .collect(Collectors.toList()))
                .addAllObServiceConfigs(serviceOBConfigs)
                .setPagination(response.getPagination())
                .build();
    }

    public GetPetAvailableGroupClassInstancesResult getAvailableGroupClassInstances(
            long companyId, long businessId, long groupClassId, PaginationRequest pagination) {
        final var instanceRequest = ListInstancesRequest.newBuilder()
                .setPagination(pagination)
                .setCompanyId(companyId)
                .addBusinessIds(businessId)
                .addGroupClassIds(groupClassId)
                .setStatus(Status.UPCOMING)
                .build();

        final var instanceResponse = groupClassService.listInstances(instanceRequest);
        if (Objects.isNull(instanceResponse.getGroupClassInstancesList())) {
            return GetPetAvailableGroupClassInstancesResult.newBuilder()
                    .addAllGroupClassInstances(Collections.emptyList())
                    .setPagination(instanceResponse.getPagination())
                    .build();
        }

        final var sessionRequest = ListSessionsRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllGroupClassInstanceId(instanceResponse.getGroupClassInstancesList().stream()
                        .map(GroupClassInstance::getId)
                        .collect(Collectors.toList()))
                .build();

        final var sessionResponse = groupClassService.listSessions(sessionRequest);

        final var sessionMap = sessionResponse.getSessionsList().stream()
                .collect(Collectors.groupingBy(GroupClassSession::getGroupClassInstanceId));

        final var staffResponse = staffService.queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                .addAllStaffIds(instanceResponse.getGroupClassInstancesList().stream()
                        .map(GroupClassInstance::getStaffId)
                        .collect(Collectors.toList()))
                .build());

        final var staffMap = staffResponse.getStaffsList().stream()
                .collect(Collectors.toMap(StaffModel::getId, Function.identity()));

        return GetPetAvailableGroupClassInstancesResult.newBuilder()
                .addAllGroupClassInstances(instanceResponse.getGroupClassInstancesList().stream()
                        .map(instance ->
                                GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.newBuilder()
                                        .setGroupClassInstance(instance)
                                        .addAllSessions(
                                                sessionMap.getOrDefault(instance.getId(), Collections.emptyList()))
                                        .setTrainer(TrainerView.newBuilder()
                                                .setId(instance.getStaffId())
                                                .setFirstName(staffMap.get(instance.getStaffId())
                                                        .getFirstName())
                                                .setLastName(staffMap.get(instance.getStaffId())
                                                        .getLastName())
                                                .setAvatarPath(staffMap.get(instance.getStaffId())
                                                        .getAvatarPath())
                                                .build())
                                        .build())
                        .collect(Collectors.toList()))
                .setPagination(instanceResponse.getPagination())
                .build();
    }
}
