package com.moego.svc.business.customer.service;

import static com.moego.svc.business.customer.repository.jooq.tables.PetMetadata.PET_METADATA;

import com.moego.idl.models.business_customer.v1.BusinessPetMetadataName;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.business.customer.repository.jooq.tables.records.PetMetadataRecord;
import com.moego.svc.business.customer.utils.SortUtils;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.JSON;
import org.jooq.Result;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2024/1/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PetMetadataService {

    private final DSLContext dslContext;

    public Condition tenantCondition(Tenant tenant) {
        return PET_METADATA.COMPANY_ID.eq(tenant.getCompanyId());
    }

    private void checkMetadataValue(PetMetadataRecord record) {
        var condition = PET_METADATA
                .COMPANY_ID
                .eq(record.getCompanyId())
                .and(PET_METADATA.METADATA_NAME.eq(record.getMetadataName()))
                .and(PET_METADATA.METADATA_VALUE.eq(record.getMetadataValue()))
                .and(PET_METADATA.DELETED_AT.isNull());
        Result<PetMetadataRecord> result =
                dslContext.selectFrom(PET_METADATA).where(condition).fetch();
        // new record or update record
        boolean exists = (record.getId() == null && !result.isEmpty())
                || !result.stream().allMatch(r -> Objects.equals(r.getId(), record.getId()));
        if (exists) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Value already exists");
        }
    }

    @Transactional
    public PetMetadataRecord create(PetMetadataRecord record) {
        checkMetadataValue(record);
        // select max sort
        Integer maxSort = dslContext
                .select(DSL.max(PET_METADATA.SORT))
                .from(PET_METADATA)
                .where(PET_METADATA.COMPANY_ID.eq(record.getCompanyId()))
                .and(PET_METADATA.METADATA_NAME.eq(record.getMetadataName()))
                .fetchOneInto(Integer.class);
        record.setSort(maxSort == null ? 1 : maxSort + 1);
        return dslContext.insertInto(PET_METADATA).set(record).returning().fetchOne();
    }

    @Transactional
    public void update(PetMetadataRecord record) {
        checkMetadataValue(record);
        dslContext
                .update(PET_METADATA)
                .set(PET_METADATA.METADATA_VALUE, record.getMetadataValue())
                .set(PET_METADATA.EXTRA_JSON, record.getExtraJson())
                .where(PET_METADATA.ID.eq(record.getId()))
                .and(PET_METADATA.COMPANY_ID.eq(record.getCompanyId()))
                .execute();
    }

    public void delete(Long companyId, Long id) {
        dslContext
                .update(PET_METADATA)
                .set(PET_METADATA.DELETED_AT, LocalDateTime.now())
                .where(PET_METADATA.ID.eq(id))
                .and(PET_METADATA.COMPANY_ID.eq(companyId))
                .execute();
    }

    public List<PetMetadataRecord> list(Long companyId, List<BusinessPetMetadataName> names) {
        return dslContext
                .selectFrom(PET_METADATA)
                .where(PET_METADATA.COMPANY_ID.eq(companyId))
                .and(PET_METADATA.METADATA_NAME.in(
                        names.stream().map(BusinessPetMetadataName::getNumber).toList()))
                .and(PET_METADATA.DELETED_AT.isNull())
                .fetch();
    }

    @Transactional
    public void sort(Long companyId, BusinessPetMetadataName name, List<Long> ids) {
        List<Long> existsIds = list(companyId, List.of(name)).stream()
                .map(PetMetadataRecord::getId)
                .sorted()
                .toList();
        List<Long> sortIds = ids.stream().sorted().toList();
        if (!Objects.equals(existsIds, sortIds)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "sort ids not match");
        }
        // id -> sort
        Map<Long, Integer> sortMap = SortUtils.toSortMap(ids);
        var sortRecords = sortMap.entrySet().stream()
                .map(entry -> new PetMetadataRecord().setId(entry.getKey()).setSort(entry.getValue()))
                .toList();
        dslContext.batchUpdate(sortRecords).execute();
    }

    public boolean needInit(Long companyId) {
        Integer count = dslContext
                .selectCount()
                .from(PET_METADATA)
                .where(PET_METADATA.COMPANY_ID.eq(companyId))
                .fetchOneInto(Integer.class);
        return count == null || count == 0;
    }

    public void init(Long companyId) {
        var records = buildInitRecord(companyId).values().stream()
                .flatMap(List::stream)
                .toList();
        dslContext.batchInsert(records).execute();
    }

    static Map<BusinessPetMetadataName, List<PetMetadataRecord>> buildInitRecord(Long companyId) {
        Map<String, String> am = Map.of("label", "AM");
        Map<String, String> noon = Map.of("label", "Noon");
        Map<String, String> pm = Map.of("label", "PM");
        Map<BusinessPetMetadataName, List<PetMetadataRecord>> map = new HashMap<>();
        map.put(
                BusinessPetMetadataName.FEEDING_SCHEDULE,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_SCHEDULE_VALUE)
                                .setMetadataValue("540")
                                .setExtraJson(JSON.valueOf(JsonUtil.toJson(am)))
                                .setSort(3),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_SCHEDULE_VALUE)
                                .setMetadataValue("720")
                                .setExtraJson(JSON.valueOf(JsonUtil.toJson(noon)))
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_SCHEDULE_VALUE)
                                .setMetadataValue("1080")
                                .setExtraJson(JSON.valueOf(JsonUtil.toJson(pm)))
                                .setSort(1)));
        map.put(
                BusinessPetMetadataName.FEEDING_UNIT,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_UNIT_VALUE)
                                .setMetadataValue("Oz")
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_UNIT_VALUE)
                                .setMetadataValue("Cup")
                                .setSort(1)));
        map.put(
                BusinessPetMetadataName.FEEDING_TYPE,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_TYPE_VALUE)
                                .setMetadataValue("Wet food")
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_TYPE_VALUE)
                                .setMetadataValue("Dry food")
                                .setSort(1)));
        map.put(
                BusinessPetMetadataName.FEEDING_SOURCE,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_SOURCE_VALUE)
                                .setMetadataValue("Owner provide")
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_SOURCE_VALUE)
                                .setMetadataValue("House provide")
                                .setSort(1)));
        map.put(
                BusinessPetMetadataName.FEEDING_INSTRUCTION,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_INSTRUCTION_VALUE)
                                .setMetadataValue("Free feed")
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_INSTRUCTION_VALUE)
                                .setMetadataValue("Feed alone")
                                .setSort(1)));
        map.put(
                BusinessPetMetadataName.MEDICATION_SCHEDULE,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_SCHEDULE_VALUE)
                                .setMetadataValue("540")
                                .setExtraJson(JSON.valueOf(JsonUtil.toJson(am)))
                                .setSort(3),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_SCHEDULE_VALUE)
                                .setMetadataValue("720")
                                .setExtraJson(JSON.valueOf(JsonUtil.toJson(noon)))
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_SCHEDULE_VALUE)
                                .setMetadataValue("1080")
                                .setExtraJson(JSON.valueOf(JsonUtil.toJson(pm)))
                                .setSort(1)));
        map.put(
                BusinessPetMetadataName.MEDICATION_UNIT,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_UNIT_VALUE)
                                .setMetadataValue("Oz")
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_UNIT_VALUE)
                                .setMetadataValue("Cup")
                                .setSort(1)));
        map.put(
                BusinessPetMetadataName.FEEDING_FEEDBACK,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE)
                                .setMetadataValue("\uD83C\uDF7D\uFE0F Ate all (100%)")
                                .setSort(5),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE)
                                .setMetadataValue("\uD83D\uDE0B Ate most (70%)")
                                .setSort(4),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE)
                                .setMetadataValue("\uD83E\uDD44 Ate some (50%)")
                                .setSort(3),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE)
                                .setMetadataValue("\uD83D\uDC45 Ate a little (25%)")
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE)
                                .setMetadataValue("❌ Refused to eat (0%)")
                                .setSort(1)));
        map.put(
                BusinessPetMetadataName.MEDICATION_FEEDBACK,
                List.of(
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_FEEDBACK_VALUE)
                                .setMetadataValue("\uD83D\uDC8A Took all on time")
                                .setSort(4),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_FEEDBACK_VALUE)
                                .setMetadataValue("\uD83D\uDE42 Took partial dose")
                                .setSort(3),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_FEEDBACK_VALUE)
                                .setMetadataValue("\uD83E\uDDC1 Administered in treat")
                                .setSort(2),
                        new PetMetadataRecord()
                                .setCompanyId(companyId)
                                .setMetadataName(BusinessPetMetadataName.MEDICATION_FEEDBACK_VALUE)
                                .setMetadataValue("\uD83D\uDE4B\u200D♀\uFE0F Need assistance")
                                .setSort(1)));
        return map;
    }
}
