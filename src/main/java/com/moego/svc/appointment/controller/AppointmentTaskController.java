package com.moego.svc.appointment.controller;

import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.moeGroomingAppointment;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.serviceTypeInclude;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.moeGroomingPetDetail;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.status;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;

import com.moego.common.enums.ClientApptConst;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.models.appointment.v1.PetDetailStatus;
import com.moego.idl.service.appointment.v1.AppointmentTaskServiceGrpc;
import com.moego.idl.service.appointment.v1.BatchPatchAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.BatchPatchAppointmentTaskResponse;
import com.moego.idl.service.appointment.v1.CountAppointmentTasksRequest;
import com.moego.idl.service.appointment.v1.CountAppointmentTasksResponse;
import com.moego.idl.service.appointment.v1.DeleteAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.DeleteAppointmentTaskResponse;
import com.moego.idl.service.appointment.v1.GetAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentTaskResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskGroupsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskGroupsResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskPetsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskPetsResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentTasksRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTasksResponse;
import com.moego.idl.service.appointment.v1.PatchAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.PatchAppointmentTaskResponse;
import com.moego.idl.service.appointment.v1.PatchTasksByAppointmentRequest;
import com.moego.idl.service.appointment.v1.PatchTasksByAppointmentResponse;
import com.moego.idl.service.appointment.v1.SyncAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.SyncAppointmentTaskResponse;
import com.moego.idl.service.appointment.v1.UpdateAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentTaskResponse;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.svc.appointment.converter.AppointmentTaskConverter;
import com.moego.svc.appointment.domain.AppointmentTask;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.dto.AppointmentTaskCountDTO;
import com.moego.svc.appointment.dto.ListAppointmentTaskDTO;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentMapper;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailMapper;
import com.moego.svc.appointment.service.AppointmentTaskService;
import com.moego.svc.appointment.service.remote.CompanyRemoteService;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/12/3
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AppointmentTaskController extends AppointmentTaskServiceGrpc.AppointmentTaskServiceImplBase {

    private final MoeGroomingAppointmentMapper appointmentMapper;
    private final MoeGroomingPetDetailMapper petDetailMapper;
    private final AppointmentTaskService appointmentTaskService;

    private final CompanyRemoteService companyRemoteService;

    @Override
    public void listAppointmentTasks(
            ListAppointmentTasksRequest request, StreamObserver<ListAppointmentTasksResponse> responseObserver) {
        var dto = new ListAppointmentTaskDTO()
                .setCompanyId(request.getCompanyId())
                .setBusinessIds(request.getBusinessIdsList())
                .setFilter(request.getFilter())
                .setOrderBys(buildOrderBys(request))
                .setPagination(request.getPagination());
        var listPair = appointmentTaskService.listAppointmentTasks(dto);

        responseObserver.onNext(ListAppointmentTasksResponse.newBuilder()
                .addAllTasks(AppointmentTaskConverter.INSTANCE.toModel(listPair.first()))
                .setPagination(listPair.second())
                .build());
        responseObserver.onCompleted();
    }

    static List<OrderBy> buildOrderBys(ListAppointmentTasksRequest request) {
        if (request.getOrderBysCount() != 0) {
            return request.getOrderBysList();
        }
        if (request.hasOrderBy()) {
            return List.of(request.getOrderBy());
        }
        return List.of(
                OrderBy.newBuilder().setFieldName("startTime").setAsc(true).build());
    }

    @Override
    public void countAppointmentTasks(
            CountAppointmentTasksRequest request, StreamObserver<CountAppointmentTasksResponse> responseObserver) {
        var timezoneName = companyRemoteService.getTimezoneName(request.getCompanyId());
        var dto = new AppointmentTaskCountDTO()
                .setCompanyId(request.getCompanyId())
                .setBusinessId(request.getBusinessId())
                .setFilter(request.getFilter())
                .setTimeZoneName(timezoneName);

        var categoryResult = CompletableFuture.supplyAsync(
                () -> appointmentTaskService.countByCategory(dto, request.getGroupCategoriesList()),
                ThreadPool.getSubmitExecutor());
        var scheduleResult = CompletableFuture.supplyAsync(
                () -> appointmentTaskService.countBySchedule(dto, request.getGroupSchedulesList()),
                ThreadPool.getSubmitExecutor());
        var careTypeResult = CompletableFuture.supplyAsync(
                () -> appointmentTaskService.countByCareType(dto, request.getGroupCareTypesList()),
                ThreadPool.getSubmitExecutor());
        var statusResult = CompletableFuture.supplyAsync(
                () -> appointmentTaskService.countByStatus(dto, request.getGroupStatusList()),
                ThreadPool.getSubmitExecutor());
        var countResult = CompletableFuture.supplyAsync(
                () -> appointmentTaskService.countByFilter(dto), ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(careTypeResult, scheduleResult, categoryResult, statusResult, countResult)
                .join();

        responseObserver.onNext(CountAppointmentTasksResponse.newBuilder()
                .addAllCategoryCounts(categoryResult.join())
                .addAllScheduleCounts(scheduleResult.join())
                .addAllCareTypeCounts(careTypeResult.join())
                .addAllStatusCounts(statusResult.join())
                .setTotalCount(countResult.join().intValue())
                .build());
        responseObserver.onCompleted();
    }

    @Nullable
    private Long getAssignedStaff(PatchAppointmentTaskRequest request) {
        var originalTask = appointmentTaskService.mustGet(request.getTaskId());
        if (request.hasStaffId()) {
            return request.getStaffId();
        }
        if (PrimitiveTypeUtil.isNullOrZero(originalTask.getStaffId())
                && Objects.equals(request.getStatus(), AppointmentTaskStatus.COMPLETED)) {
            // complete task 时自动 assign 当前 token staff
            return request.getTokenStaffId();
        }
        return null;
    }

    @Override
    public void patchAppointmentTask(
            PatchAppointmentTaskRequest request, StreamObserver<PatchAppointmentTaskResponse> responseObserver) {
        var task = new AppointmentTask();
        task.setId(request.getTaskId());
        task.setStaffId(getAssignedStaff(request));
        if (request.hasStatus()) {
            task.setStatus(request.getStatus().name());
        }
        switch (request.getNoteStatusCase()) {
            case FEEDING_NOTE_STATUS -> task.setNoteStatus(
                    request.getFeedingNoteStatus().name());
            case MEDICATION_NOTE_STATUS -> task.setNoteStatus(
                    request.getMedicationNoteStatus().name());
            case CUSTOMIZED_NOTE_STATUS -> task.setNoteStatus(request.getCustomizedNoteStatus());
            default -> {} // No-op
        }
        if (request.hasNoteContent()) {
            task.setNoteContent(request.getNoteContent());
        }

        if (request.hasNoteFeedback()) {
            task.setNoteFeedback(request.getNoteFeedback());
        }

        int updatedCount = appointmentTaskService.updateSelective(task);

        responseObserver.onNext(PatchAppointmentTaskResponse.newBuilder()
                .setUpdated(updatedCount != 0)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchPatchAppointmentTask(
            BatchPatchAppointmentTaskRequest request,
            StreamObserver<BatchPatchAppointmentTaskResponse> responseObserver) {
        var task = new AppointmentTask();
        if (request.hasStaffId()) {
            task.setStaffId(request.getStaffId());
        }
        if (request.hasStatus()) {
            task.setStatus(request.getStatus().name());
        }

        // Update by task ids and filter
        int affectedRow = appointmentTaskService.updateSelectiveByIdsAndFilter(
                request.getTenant(),
                task,
                request.getFilter(),
                request.getTaskIdsList(),
                request.getGroupFiltersList());

        responseObserver.onNext(BatchPatchAppointmentTaskResponse.newBuilder()
                .setAffectedRow(affectedRow)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAppointmentTask(
            UpdateAppointmentTaskRequest request, StreamObserver<UpdateAppointmentTaskResponse> responseObserver) {

        var entity = AppointmentTaskConverter.INSTANCE.toEntity(request);

        var affectedRow = appointmentTaskService.updateSelective(entity);

        responseObserver.onNext(UpdateAppointmentTaskResponse.newBuilder()
                .setUpdated(affectedRow == 1)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAppointmentTask(
            GetAppointmentTaskRequest request, StreamObserver<GetAppointmentTaskResponse> responseObserver) {

        var entity = appointmentTaskService.mustGet(request.getTaskId());

        responseObserver.onNext(GetAppointmentTaskResponse.newBuilder()
                .setTask(AppointmentTaskConverter.INSTANCE.toModel(entity))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listAppointmentTaskGroups(
            ListAppointmentTaskGroupsRequest request,
            StreamObserver<ListAppointmentTaskGroupsResponse> responseObserver) {
        var timezoneName = companyRemoteService.getTimezoneName(request.getCompanyId());
        var dto = new AppointmentTaskCountDTO()
                .setCompanyId(request.getCompanyId())
                .setBusinessId(request.getBusinessId())
                .setFilter(AppointmentTaskConverter.INSTANCE.toFilter(request.getFilter()))
                .setTimeZoneName(timezoneName);

        var groups =
                switch (request.getTabCase()) {
                    case CATEGORY -> {
                        switch (request.getCategory()) {
                            case FEEDING, MEDICATION:
                                yield appointmentTaskService.getTimeSlotGroups(dto, request.getCategory());
                            case ADD_ONS:
                                yield appointmentTaskService.getAddOnGroups(dto, request.getCategory());
                            default:
                                yield new ArrayList<ListAppointmentTaskGroupsResponse.Group>();
                        }
                    }
                    case SCHEDULE -> {
                        switch (request.getSchedule()) {
                            case AM, PM:
                                yield appointmentTaskService.getCategoryGroups(dto, request.getSchedule());
                            case UNASSIGNED:
                                yield appointmentTaskService.getAddOnGroups(dto, request.getSchedule());
                            default:
                                yield new ArrayList<ListAppointmentTaskGroupsResponse.Group>();
                        }
                    }
                    default -> new ArrayList<ListAppointmentTaskGroupsResponse.Group>(); // No-op
                };

        responseObserver.onNext(ListAppointmentTaskGroupsResponse.newBuilder()
                .addAllGroups(groups)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listAppointmentTaskPets(
            ListAppointmentTaskPetsRequest request, StreamObserver<ListAppointmentTaskPetsResponse> responseObserver) {
        var petIds = appointmentTaskService.listAppointmentTaskPets(
                request.getCompanyId(), request.getBusinessId(), request.getFilter());

        responseObserver.onNext(ListAppointmentTaskPetsResponse.newBuilder()
                .addAllPetIds(petIds)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteAppointmentTask(
            DeleteAppointmentTaskRequest request, StreamObserver<DeleteAppointmentTaskResponse> responseObserver) {
        int affectedRows = appointmentTaskService.deleteByFilter(request.getFilter());

        responseObserver.onNext(DeleteAppointmentTaskResponse.newBuilder()
                .setAffectedRow(affectedRows)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void patchTasksByAppointment(
            PatchTasksByAppointmentRequest request, StreamObserver<PatchTasksByAppointmentResponse> responseObserver) {
        var task = new AppointmentTask();
        if (request.hasStaffId()) {
            task.setStaffId(request.getStaffId());
        }
        if (request.hasStatus()) {
            task.setStatus(request.getStatus().name());
        }
        int affectedRows = appointmentTaskService.updateByAppointment(request.getAppointmentId(), task);

        responseObserver.onNext(PatchTasksByAppointmentResponse.newBuilder()
                .setAffectedRow(affectedRows)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void syncAppointmentTask(
            SyncAppointmentTaskRequest request, StreamObserver<SyncAppointmentTaskResponse> responseObserver) {

        int startAppointmentId = request.getStartAppointmentId();
        int endAppointmentId = request.getEndAppointmentId();
        int threadCount = request.hasThreadCount() ? request.getThreadCount() : 1;
        String syncStartDate = request.hasSyncStartDate()
                ? request.getSyncStartDate()
                : LocalDate.now().toString();

        int totalRange = endAppointmentId - startAppointmentId + 1;
        int rangePerThread = totalRange / threadCount;

        for (int i = 0; i < threadCount; i++) {
            int threadStartId = startAppointmentId + (i * rangePerThread);
            int threadEndId = (i == threadCount - 1) ? endAppointmentId : threadStartId + rangePerThread - 1;

            ThreadPool.execute(() -> processAppointmentIdsInBatch(threadStartId, threadEndId, syncStartDate));
        }

        responseObserver.onNext(SyncAppointmentTaskResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void processAppointmentIdsInBatch(int startAppointmentId, int endAppointmentId, String syncStartDate) {
        final int pageSize = 1000;
        var offset = new AtomicInteger(startAppointmentId);
        while (offset.get() <= endAppointmentId) {
            var appointments = appointmentMapper.select(
                    c -> c.where(moeGroomingAppointment.id, SqlBuilder.isGreaterThan(offset.get()))
                            .and(moeGroomingAppointment.id, SqlBuilder.isLessThanOrEqualTo(offset.get() + pageSize))
                            .and(moeGroomingAppointment.isBlock, SqlBuilder.isEqualTo(ClientApptConst.IS_BLOCK_FALSE))
                            .and(
                                    moeGroomingAppointment.appointmentEndDate,
                                    SqlBuilder.isGreaterThanOrEqualTo(syncStartDate))
                            .and(serviceTypeInclude, SqlBuilder.isNotIn(1, 8)) // filter grooming, evaluation
                    );
            log.info(
                    "[SyncAppointmentTask] from [{}] to [{}], size [{}]",
                    offset.get(),
                    offset.get() + pageSize,
                    appointments.size());
            offset.set(offset.get() + pageSize);
            if (appointments.isEmpty()) {
                continue;
            }

            processAppointmentBatch(appointments);
        }
    }

    private void processAppointmentBatch(List<MoeGroomingAppointment> appointments) {
        var appointmentIds =
                appointments.stream().map(MoeGroomingAppointment::getId).toList();

        var appointmentIdToPetDetails = petDetailMapper
                .select(c -> c.where(moeGroomingPetDetail.groomingId, SqlBuilder.isInWhenPresent(appointmentIds))
                        .and(status, isNotEqualTo((byte) PetDetailStatus.DELETED_VALUE)))
                .stream()
                .collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));

        appointments.forEach(appointment -> processAppointment(appointment, appointmentIdToPetDetails));
    }

    private void processAppointment(
            MoeGroomingAppointment appointment, Map<Integer, List<MoeGroomingPetDetail>> appointmentIdToPetDetails) {
        try {
            // Delete tasks if appointment is canceled or deprecated
            if (Objects.equals(appointment.getStatus().intValue(), AppointmentStatus.CANCELED_VALUE)
                    || Objects.equals(appointment.getIsDeprecate(), true)) {
                var result = appointmentTaskService.deleteByAppointment(appointment.getId());

                log.info(
                        "[SyncAppointmentTask] Appointment id: [{}], cancel task size: [{}]",
                        appointment.getId(),
                        result);

                return;
            }

            var oldTasks = appointmentTaskService.listByAppointmentId(appointment.getId());
            var petDetails = appointmentIdToPetDetails.getOrDefault(appointment.getId(), List.of());

            var tasks = Stream.concat(
                            appointmentTaskService.buildBasicTasks(appointment, petDetails, oldTasks).stream(),
                            appointmentTaskService.buildAddOnTasks(appointment, petDetails, oldTasks).stream())
                    .toList();

            if (!CollectionUtils.isEmpty(tasks)) {
                appointmentTaskService.idempotentInsertMultiple(appointment.getId(), tasks);

                log.info(
                        "[SyncAppointmentTask] Appointment id: [{}], recreate task size: [{}]",
                        appointment.getId(),
                        tasks.size());
            }
        } catch (Exception e) {
            log.error("[SyncAppointmentTask] Failed to sync for appointment id: [{}]", appointment.getId(), e);
        }
    }
}
