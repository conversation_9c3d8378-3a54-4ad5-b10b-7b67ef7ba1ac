package com.moego.svc.appointment.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.appointment.utils.PetDetailUtil.buildDateTime;
import static com.moego.svc.appointment.utils.PetDetailUtil.calculateAppointmentTime;
import static com.moego.svc.appointment.utils.PetDetailUtil.calculateEndDateAndEndTime;
import static com.moego.svc.appointment.utils.PetDetailUtil.calculateOffsetMinute;
import static com.moego.svc.appointment.utils.PetDetailUtil.getBoardingConflictService;
import static com.moego.svc.appointment.utils.PetDetailUtil.getDateTypeMap;
import static com.moego.svc.appointment.utils.PetDetailUtil.getDaycareConflictService;
import static com.moego.svc.appointment.utils.PetDetailUtil.getStaffIdList;
import static com.moego.svc.appointment.utils.PetDetailUtil.hasBoarding;
import static com.moego.svc.appointment.utils.PetDetailUtil.isGroomingOnly;
import static com.moego.svc.appointment.utils.PetDetailUtil.offsetPetDetailsAndOperations;

import com.moego.common.enums.AppointmentEventEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.AddOnScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentPetScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentScheduleDef;
import com.moego.idl.models.appointment.v1.CalendarCardType;
import com.moego.idl.models.appointment.v1.GroomingServiceCalendarDef;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.PetDetailScheduleDef;
import com.moego.idl.models.appointment.v1.PetScheduleDef;
import com.moego.idl.models.appointment.v1.PetServiceCalendarDef;
import com.moego.idl.models.appointment.v1.PetServiceScheduleDef;
import com.moego.idl.models.appointment.v1.RepeatAppointmentModifyScope;
import com.moego.idl.models.appointment.v1.ServiceLodgingAssignDef;
import com.moego.idl.models.appointment.v1.ServiceScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.appointment.v1.AppointmentScheduleServiceGrpc;
import com.moego.idl.service.appointment.v1.BatchGetPetFeedingMedicationSchedulesRequest;
import com.moego.idl.service.appointment.v1.BatchGetPetFeedingMedicationSchedulesResponse;
import com.moego.idl.service.appointment.v1.BatchRescheduleAppointmentRequest;
import com.moego.idl.service.appointment.v1.BatchRescheduleAppointmentResponse;
import com.moego.idl.service.appointment.v1.CalculateAppointmentScheduleRequest;
import com.moego.idl.service.appointment.v1.CalculateAppointmentScheduleResponse;
import com.moego.idl.service.appointment.v1.GetPetFeedingMedicationSchedulesRequest;
import com.moego.idl.service.appointment.v1.GetPetFeedingMedicationSchedulesResponse;
import com.moego.idl.service.appointment.v1.LodgingAssignRequest;
import com.moego.idl.service.appointment.v1.LodgingAssignResponse;
import com.moego.idl.service.appointment.v1.PreviewCalendarScheduleRequest;
import com.moego.idl.service.appointment.v1.PreviewCalendarScheduleResponse;
import com.moego.idl.service.appointment.v1.RescheduleBoardingServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleBoardingServiceResponse;
import com.moego.idl.service.appointment.v1.RescheduleCalendarCardRequest;
import com.moego.idl.service.appointment.v1.RescheduleCalendarCardResponse;
import com.moego.idl.service.appointment.v1.RescheduleDaycareServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleDaycareServiceResponse;
import com.moego.idl.service.appointment.v1.RescheduleEvaluationServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleEvaluationServiceResponse;
import com.moego.idl.service.appointment.v1.RescheduleGroomingServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleGroomingServiceResponse;
import com.moego.idl.service.appointment.v1.ReschedulePetDetailsRequest;
import com.moego.idl.service.appointment.v1.ReschedulePetDetailsResponse;
import com.moego.idl.service.appointment.v1.ReschedulePetFeedingMedicationRequest;
import com.moego.idl.service.appointment.v1.ReschedulePetFeedingMedicationResponse;
import com.moego.idl.service.appointment.v1.ReschedulePetFeedingRequest;
import com.moego.idl.service.appointment.v1.ReschedulePetFeedingResponse;
import com.moego.idl.service.appointment.v1.ReschedulePetMedicationRequest;
import com.moego.idl.service.appointment.v1.ReschedulePetMedicationResponse;
import com.moego.idl.service.appointment.v1.SwitchAllPetsStartAtSameTimeRequest;
import com.moego.idl.service.appointment.v1.SwitchAllPetsStartAtSameTimeResponse;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import com.moego.svc.appointment.converter.AppointmentConverter;
import com.moego.svc.appointment.converter.DateConverter;
import com.moego.svc.appointment.converter.GroomingServiceOperationConverter;
import com.moego.svc.appointment.converter.PetDetailConverter;
import com.moego.svc.appointment.converter.PetFeedingConverter;
import com.moego.svc.appointment.converter.PetMedicationConverter;
import com.moego.svc.appointment.converter.PetScheduleSettingConverter;
import com.moego.svc.appointment.domain.AppointmentPetFeeding;
import com.moego.svc.appointment.domain.AppointmentPetMedication;
import com.moego.svc.appointment.domain.AppointmentPetScheduleSetting;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.dto.GroomingOnlyDTO;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.dto.RescheduleCalendarCardDTO;
import com.moego.svc.appointment.dto.RescheduleGroomingDTO;
import com.moego.svc.appointment.listener.event.RescheduleGroomingServiceEvent;
import com.moego.svc.appointment.listener.event.ReschedulePetDetailsEvent;
import com.moego.svc.appointment.listener.event.ReschedulePetFeedingMedicationEvent;
import com.moego.svc.appointment.service.AppointmentCompositeService;
import com.moego.svc.appointment.service.AppointmentPetFeedingService;
import com.moego.svc.appointment.service.AppointmentPetMedicationService;
import com.moego.svc.appointment.service.AppointmentPetScheduleService;
import com.moego.svc.appointment.service.AppointmentPetScheduleSettingService;
import com.moego.svc.appointment.service.AppointmentRescheduleService;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.EvaluationServiceDetailService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.PricingRuleRecordApplyService;
import com.moego.svc.appointment.service.RepeatAppointmentService;
import com.moego.svc.appointment.service.ServiceOperationService;
import com.moego.svc.appointment.service.remote.ActiveMQService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import com.moego.svc.appointment.utils.Pair;
import com.moego.svc.appointment.utils.PetDetailUtil;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/2/18
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AppointmentScheduleController extends AppointmentScheduleServiceGrpc.AppointmentScheduleServiceImplBase {

    private final AppointmentCompositeService appointmentCompositeService;
    private final AppointmentServiceProxy appointmentService;
    private final PetDetailServiceProxy petDetailService;
    private final EvaluationServiceDetailService petEvaluationService;
    private final OfferingRemoteService offeringRemoteService;
    private final AppointmentPetFeedingService petFeedingService;
    private final AppointmentPetMedicationService petMedicationService;
    private final AppointmentPetScheduleSettingService petScheduleSettingService;

    private final PetFeedingConverter feedingConverter;
    private final PetMedicationConverter medicationConverter;
    private final PetScheduleSettingConverter scheduleSettingConverter;
    private final OrderRemoteService orderRemoteService;
    private final AppointmentPetScheduleService petScheduleService;
    private final ApplicationEventPublisher publisher;
    private final ServiceOperationService serviceOperationService;
    private final AppointmentRescheduleService appointmentRescheduleService;
    private final ActiveMQService mqService;
    private final RepeatAppointmentService repeatAppointmentService;
    private final PricingRuleRecordApplyService pricingRuleApplyService;

    @Override
    public void rescheduleBoardingService(
            RescheduleBoardingServiceRequest request,
            StreamObserver<RescheduleBoardingServiceResponse> responseObserver) {
        List<MoeGroomingPetDetail> updateDetails =
                PetDetailConverter.INSTANCE.toDomain(request.getBoardingServiceSchedulesList());
        var beforePetDetails = petDetailService.getPetDetailList(request.getAppointmentId());
        petDetailService.fixServiceType(request.getCompanyId(), beforePetDetails);

        // 校验 Grooming & Daycare service 时间是否在 boarding 时间范围内
        List<MoeGroomingPetDetail> conflictDetails = updateDetails.stream()
                .map(k -> getBoardingConflictService(k, beforePetDetails))
                .flatMap(List::stream)
                .toList();
        if (!CollectionUtils.isEmpty(conflictDetails)) {
            responseObserver.onNext(RescheduleBoardingServiceResponse.newBuilder()
                    .addAllConflictServiceNames(buildConflictMessage(request.getCompanyId(), conflictDetails))
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // 1. 更新 pet detail
        Map<Integer, MoeGroomingPetDetail> petBoardingServiceMap = beforePetDetails.stream()
                .filter(petDetail ->
                        Objects.equals(petDetail.getServiceItemType().intValue(), ServiceItemType.BOARDING_VALUE))
                .collect(Collectors.toMap(MoeGroomingPetDetail::getPetId, Function.identity(), (d1, d2) -> d1));
        updateDetails.forEach(update -> {
            MoeGroomingPetDetail original = petBoardingServiceMap.get(update.getPetId());
            if (original == null) {
                throw bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST);
            }
            update.setId(original.getId());
            update.setQuantity(getBoardingQuantity(original, update));
        });
        petDetailService.updatePetDetailById(updateDetails);
        // 2. 其他 pet 联动更新，偏移 boarding 时间
        MoeGroomingPetDetail updatePetDetail = updateDetails.get(0);
        Set<Integer> petIds =
                updateDetails.stream().map(MoeGroomingPetDetail::getPetId).collect(Collectors.toSet());
        petBoardingServiceMap.values().forEach(original -> {
            if (!petIds.contains(original.getPetId())) {
                MoeGroomingPetDetail update = new MoeGroomingPetDetail();
                update.setId(original.getId());
                update.setStartDate(updatePetDetail.getStartDate());
                update.setStartTime(updatePetDetail.getStartTime());
                update.setEndDate(updatePetDetail.getEndDate());
                update.setEndTime(updatePetDetail.getEndTime());
                update.setQuantity(getBoardingQuantity(original, update));
                petDetailService.update(update);
            }
        });

        ThreadPool.execute(() -> {
            // 3. 应用 pricing rule
            pricingRuleApplyService.applyPricingRule(
                    request.getAppointmentId(), request.getCompanyId(), request.getBusinessId());
            // 4. 更新 order
            MoeGroomingAppointment appointment =
                    appointmentService.getAppointment(request.getCompanyId(), request.getAppointmentId());
            orderRemoteService.updateOrder(appointment);
            // 5. Publish update appointment event
            mqService.publishAppointmentEvent(request.getAppointmentId(), AppointmentEventEnum.MODIFY_SINGLE);
            // 6. publish appointment reschedule event
            publisher.publishEvent(new ReschedulePetDetailsEvent(this)
                    .setCompanyId(request.getCompanyId())
                    .setBusinessId(request.getBusinessId())
                    .setAppointmentId(request.getAppointmentId())
                    .setStaffId(request.getStaffId())
                    .setBeforePetDetails(beforePetDetails));
        });

        responseObserver.onNext(RescheduleBoardingServiceResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    static int getBoardingQuantity(MoeGroomingPetDetail original, MoeGroomingPetDetail update) {
        var petDetail = PetDetailConverter.INSTANCE.copy(original);
        petDetail.setStartDate(update.getStartDate());
        petDetail.setStartTime(update.getStartTime());
        petDetail.setEndDate(update.getEndDate());
        petDetail.setEndTime(update.getEndTime());
        return PetDetailUtil.getQuantity(petDetail, Map.of());
    }

    @Override
    public void rescheduleDaycareService(
            RescheduleDaycareServiceRequest request,
            StreamObserver<RescheduleDaycareServiceResponse> responseObserver) {
        List<MoeGroomingPetDetail> updateDetails =
                PetDetailConverter.INSTANCE.daycareServiceScheduleToDomain(request.getDaycareServiceSchedulesList());
        var beforePetDetails = petDetailService.getPetDetailList(request.getAppointmentId());
        petDetailService.fixServiceType(request.getCompanyId(), beforePetDetails);
        Map<Integer, MoeGroomingPetDetail> petDaycareServiceMap = beforePetDetails.stream()
                .filter(petDetail ->
                        Objects.equals(petDetail.getServiceItemType().intValue(), ServiceItemType.DAYCARE_VALUE))
                .filter(petDetail -> Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_VALUE))
                .collect(Collectors.toMap(MoeGroomingPetDetail::getPetId, Function.identity(), (d1, d2) -> d1));

        // 校验 Daycare service 时间是否在 boarding 时间范围内
        // 校验 Grooming service 时间是否在 daycare 时间范围内
        List<MoeGroomingPetDetail> conflictDetails = updateDetails.stream()
                .map(update -> {
                    MoeGroomingPetDetail toCheck = PetDetailConverter.INSTANCE.entityToEntity(update);
                    MoeGroomingPetDetail daycareDetail = petDaycareServiceMap.get(toCheck.getPetId());
                    if (daycareDetail == null) {
                        throw bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST);
                    }

                    toCheck.setServiceType(daycareDetail.getServiceType());
                    toCheck.setServiceItemType(daycareDetail.getServiceItemType());
                    return getDaycareConflictService(toCheck, beforePetDetails);
                })
                .flatMap(List::stream)
                .toList();
        if (!CollectionUtils.isEmpty(conflictDetails)) {
            responseObserver.onNext(RescheduleDaycareServiceResponse.newBuilder()
                    .addAllConflictServiceNames(buildConflictMessage(request.getCompanyId(), conflictDetails))
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // 1. 更新 pet detail
        updateDetails.forEach(petDetail -> {
            MoeGroomingPetDetail daycareDetail = petDaycareServiceMap.get(petDetail.getPetId());
            if (daycareDetail == null) {
                throw bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST);
            }
            petDetail.setId(daycareDetail.getId());
        });
        petDetailService.updatePetDetailById(updateDetails);
        // 2. 其他 pet 联动更新，偏移 daycare 时间
        if (!hasBoarding(beforePetDetails)) {
            MoeGroomingPetDetail updatePetDetail = updateDetails.get(0);
            Set<Integer> petIds =
                    updateDetails.stream().map(MoeGroomingPetDetail::getPetId).collect(Collectors.toSet());
            petDaycareServiceMap.values().forEach(petDetail -> {
                if (!petIds.contains(petDetail.getPetId())) {
                    MoeGroomingPetDetail update = new MoeGroomingPetDetail();
                    update.setId(petDetail.getId());
                    update.setStartDate(updatePetDetail.getStartDate());
                    update.setStartTime(updatePetDetail.getStartTime());
                    update.setEndDate(updatePetDetail.getEndDate());
                    update.setEndTime(updatePetDetail.getEndTime());
                    update.setSpecificDates(updatePetDetail.getSpecificDates());
                    petDetailService.update(update);
                }
            });
        }

        // 3. Publish update appointment event
        mqService.publishAppointmentEvent(request.getAppointmentId(), AppointmentEventEnum.MODIFY_SINGLE);

        // 4. publish appointment reschedule event
        publisher.publishEvent(new ReschedulePetDetailsEvent(this)
                .setCompanyId(request.getCompanyId())
                .setBusinessId(request.getBusinessId())
                .setAppointmentId(request.getAppointmentId())
                .setStaffId(request.getStaffId())
                .setBeforePetDetails(beforePetDetails));

        responseObserver.onNext(RescheduleDaycareServiceResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private List<String> buildConflictMessage(Long companyId, List<MoeGroomingPetDetail> conflictDetails) {
        if (CollectionUtils.isEmpty(conflictDetails)) {
            throw bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST);
        }
        List<Long> serviceIds = conflictDetails.stream()
                .map(MoeGroomingPetDetail::getServiceId)
                .distinct()
                .map(Integer::longValue)
                .toList();
        List<ServiceBriefView> serviceModels = offeringRemoteService.getServiceModels(companyId, serviceIds);
        return serviceModels.stream().map(ServiceBriefView::getName).toList();
    }

    private static final Set<CalendarCardType> NEED_END_TIME_CARDS =
            Set.of(CalendarCardType.SERVICE, CalendarCardType.OPERATION);

    private void checkRescheduleGroomingService(MoeGroomingAppointment appointment) {
        boolean hasGrooming = ServiceItemEnum.GROOMING.isIncludedIn(appointment.getServiceTypeInclude());
        if (!hasGrooming) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Only grooming service can be rescheduled.");
        }
    }

    private RescheduleGroomingDTO buildRescheduleGroomingDTO(
            RescheduleGroomingServiceRequest request,
            MoeGroomingAppointment appointment,
            List<MoeGroomingPetDetail> petDetails,
            List<MoeGroomingServiceOperation> serviceOperations) {
        var builder = RescheduleGroomingDTO.builder();
        // calculate end time
        if (!request.hasEndTime()) {
            if (NEED_END_TIME_CARDS.contains(request.getCardType())) {
                // 仅当 Appointment 和 Block 类型的 card reschedule 时，可以留空 endTime
                throw bizException(Code.CODE_PARAMS_ERROR, "endTime is required.");
            }
            int duration = calculateAppointmentTime(appointment);
            Pair<String, Integer> endDateAndEndTime =
                    calculateEndDateAndEndTime(request.getStartDate(), request.getStartTime(), duration);
            builder.endDate(endDateAndEndTime.first());
            builder.endTime(endDateAndEndTime.second());
        } else {
            builder.endTime(request.getEndTime());
        }

        // convert reschedule pet detail id to pet id and service id
        var petDetailMap = petDetails.stream()
                .collect(Collectors.toMap(detail -> detail.getId().longValue(), Function.identity()));
        if (Objects.equals(request.getCardType(), CalendarCardType.SERVICE)) {
            builder.rescheduleService(buildRescheduleService(petDetailMap, request.getId()));
        }
        if (request.getPetDetailIdsCount() != 0) {
            builder.relatedPetServices(buildRelatedPetServices(petDetailMap, request.getPetDetailIdsList()));
        }

        // convert reschedule service operation id to pet id, service id and staff id
        var operationMap = serviceOperations.stream()
                .collect(Collectors.toMap(MoeGroomingServiceOperation::getId, Function.identity()));
        if (Objects.equals(request.getCardType(), CalendarCardType.OPERATION)) {
            builder.rescheduleOperation(buildRescheduleOperation(operationMap, petDetailMap, request.getId()));
        }

        if (request.hasStaffId()) {
            builder.staffId(request.getStaffId());
        }

        return builder.companyId(request.getCompanyId())
                .appointmentId(request.getAppointmentId())
                .cardType(request.getCardType())
                .startDate(request.getStartDate())
                .startTime(request.getStartTime())
                .build();
    }

    private RescheduleGroomingDTO.RescheduleServiceDTO buildRescheduleService(
            Map<Long, MoeGroomingPetDetail> petDetailMap, long id) {
        MoeGroomingPetDetail petDetail = petDetailMap.get(id);
        if (petDetail == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Pet detail not found");
        }
        return RescheduleGroomingDTO.RescheduleServiceDTO.builder()
                .petId(petDetail.getPetId())
                .serviceId(petDetail.getServiceId())
                .build();
    }

    private List<RescheduleGroomingDTO.RescheduleServiceDTO> buildRelatedPetServices(
            Map<Long, MoeGroomingPetDetail> petDetailMap, List<Long> petDetailIds) {
        return petDetailIds.stream()
                .map(petDetailId -> {
                    MoeGroomingPetDetail petDetail = petDetailMap.get(petDetailId);
                    if (petDetail == null) {
                        throw bizException(Code.CODE_PARAMS_ERROR, "Pet detail not found");
                    }
                    return RescheduleGroomingDTO.RescheduleServiceDTO.builder()
                            .petId(petDetail.getPetId())
                            .serviceId(petDetail.getServiceId())
                            .build();
                })
                .toList();
    }

    private RescheduleGroomingDTO.RescheduleOperationDTO buildRescheduleOperation(
            Map<Long, MoeGroomingServiceOperation> operationMap,
            Map<Long, MoeGroomingPetDetail> petDetailMap,
            long id) {
        MoeGroomingServiceOperation operation = operationMap.get(id);
        if (operation == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Service operation not found");
        }
        MoeGroomingPetDetail petDetail =
                petDetailMap.get(operation.getGroomingServiceId().longValue());
        if (petDetail == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Pet detail not found");
        }
        return RescheduleGroomingDTO.RescheduleOperationDTO.builder()
                .petId(petDetail.getPetId())
                .serviceId(petDetail.getServiceId())
                .staffId(operation.getStaffId())
                .build();
    }

    @Override
    public void rescheduleGroomingService(
            RescheduleGroomingServiceRequest request,
            StreamObserver<RescheduleGroomingServiceResponse> responseObserver) {

        var appointment = appointmentService.getAppointment(request.getCompanyId(), request.getAppointmentId());

        checkRescheduleGroomingService(appointment);

        var petDetails = petDetailService.getPetDetailList(request.getAppointmentId());
        var serviceOperations = serviceOperationService.getServiceOperationList(request.getAppointmentId());

        RescheduleGroomingDTO dto = buildRescheduleGroomingDTO(request, appointment, petDetails, serviceOperations);

        String beforeDate = getStartDate(request.getCardType(), request.getId());
        appointmentRescheduleService.rescheduleCalendarCard(dto, appointment, petDetails, serviceOperations);
        String afterDate = getStartDate(request.getCardType(), request.getId());

        var event = new RescheduleGroomingServiceEvent(this)
                .setCompanyId(request.getCompanyId())
                .setBusinessId(appointment.getBusinessId().longValue())
                .setCustomerId(appointment.getCustomerId().longValue())
                .setAppointmentId(request.getAppointmentId())
                .setStaffId(request.getStaffId())
                .setRepeatAppointmentModifyScope(request.getRepeatType())
                .setRescheduleGroomingDTO(dto)
                .setOffsetDays(DateUtil.getDaysDiffByTwoDate(beforeDate, afterDate))
                .setTokenStaffId(request.getTokenStaffId())
                .setBeforeAppointmentDateTime(
                        buildDateTime(appointment.getAppointmentDate(), appointment.getAppointmentStartTime()))
                .setBeforeStaffIds(getStaffIdList(petDetails))
                .setRescheduleGroomingDTO(dto);
        if (request.hasRepeatType()) {
            event.setOffsetDays(DateUtil.getDaysDiffByTwoDate(beforeDate, afterDate));
        }
        publisher.publishEvent(event);

        responseObserver.onNext(RescheduleGroomingServiceResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private String getStartDate(CalendarCardType cardType, Long cardId) {
        return switch (cardType) {
            case APPOINTMENT, BLOCK, BOOKING_REQUEST -> appointmentService
                    .mustGet(cardId)
                    .getAppointmentDate();
            case SERVICE -> petDetailService.mustGet(cardId).getStartDate();
            case OPERATION -> {
                var operation = serviceOperationService.mustGet(cardId);
                yield petDetailService.mustGet(operation.getGroomingServiceId()).getStartDate();
            }
            default -> null;
        };
    }

    @Override
    public void rescheduleEvaluationService(
            RescheduleEvaluationServiceRequest request,
            StreamObserver<RescheduleEvaluationServiceResponse> responseObserver) {
        // 1. 获取当前 evaluation service detail
        MoeGroomingAppointment beforeAppointment =
                appointmentService.getAppointment(request.getCompanyId(), request.getAppointmentId());
        List<EvaluationServiceDetail> petEvaluationList =
                petEvaluationService.getPetEvaluationList(request.getAppointmentId());
        Map<Long, EvaluationServiceDetail> petEvaluationServiceMap = petEvaluationList.stream()
                .collect(Collectors.toMap(EvaluationServiceDetail::getId, Function.identity()));

        // 2. 更新 pet detail
        List<EvaluationServiceDetail> updateDetails = request.getEvaluationServiceSchedulesList().stream()
                .map(reschedule -> {
                    EvaluationServiceDetail evaluationServiceDetail =
                            petEvaluationServiceMap.get(reschedule.getEvaluationId());
                    if (evaluationServiceDetail == null) {
                        throw bizException(Code.CODE_PARAMS_ERROR, "Evaluation service not found");
                    }
                    LocalDateTime startAt = LocalDateTime.of(
                            LocalDate.parse(reschedule.getStartDate()),
                            LocalTime.ofSecondOfDay(reschedule.getStartTime() * 60L));

                    EvaluationServiceDetail update = new EvaluationServiceDetail();
                    update.setId(evaluationServiceDetail.getId());
                    update.setStartDate(startAt.toLocalDate());
                    update.setStartTime(DateUtil.getMinutesOfDay(startAt));
                    LocalDateTime endAt;
                    if (reschedule.hasEndTime()) {
                        endAt = LocalDateTime.of(
                                LocalDate.parse(reschedule.getStartDate()),
                                LocalTime.ofSecondOfDay(reschedule.getEndTime() * 60L));
                        update.setServiceTime(reschedule.getEndTime() - reschedule.getStartTime());
                    } else {
                        endAt = startAt.plusMinutes(evaluationServiceDetail.getServiceTime());
                    }
                    update.setEndDate(endAt.toLocalDate());
                    update.setEndTime(DateUtil.getMinutesOfDay(endAt));
                    if (reschedule.hasStaffId()) {
                        update.setStaffId(reschedule.getStaffId());
                    }
                    if (reschedule.hasLodgingId()) {
                        update.setLodgingId(reschedule.getLodgingId());
                    }
                    return update;
                })
                .toList();
        petEvaluationService.updatePetEvaluationById(updateDetails);

        // 3. 刷新 appointment 的时间
        appointmentService.refreshAppointmentDateTime(beforeAppointment);

        responseObserver.onNext(RescheduleEvaluationServiceResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void lodgingAssign(LodgingAssignRequest request, StreamObserver<LodgingAssignResponse> responseObserver) {
        long appointmentId = request.getAppointmentId();
        // 鉴权
        if (request.hasCompanyId()) {
            appointmentService.getAppointment(request.getCompanyId(), request.getAppointmentId());
        }
        var isPetDetailsOk = petDetailService.isBelongsToAppointment(
                appointmentId,
                request.getServicesList().stream()
                        .map(k -> (int) k.getPetServiceDetailId())
                        .toList());
        var isPetEvaluationsOk = petEvaluationService.isBelongsToAppointment(
                appointmentId,
                request.getEvaluationsList().stream()
                        .map(ServiceLodgingAssignDef::getPetServiceDetailId)
                        .toList());
        if (!isPetDetailsOk || !isPetEvaluationsOk) {
            throw bizException(Code.CODE_SERVICE_NOT_FOUND);
        }

        var toUpdatePetDetails = PetDetailConverter.INSTANCE.lodgingAssignToPetDetailUpdate(request.getServicesList());
        var toUpdateEvaluations =
                PetDetailConverter.INSTANCE.lodgingAssignToPetEvaluationUpdate(request.getEvaluationsList());
        petDetailService.updatePetDetailById(toUpdatePetDetails);
        // 一个预约下只有一个 Evaluation
        for (var toUpdateEvaluation : toUpdateEvaluations) {
            petEvaluationService.updatePetEvaluationById(toUpdateEvaluation);
        }

        responseObserver.onNext(LodgingAssignResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getPetFeedingMedicationSchedules(
            GetPetFeedingMedicationSchedulesRequest request,
            StreamObserver<GetPetFeedingMedicationSchedulesResponse> responseObserver) {
        List<AppointmentPetFeeding> feedings =
                petFeedingService.listPetFeedings(request.getCompanyId(), List.of(request.getAppointmentId()));
        List<AppointmentPetMedication> medications =
                petMedicationService.listPetMedications(request.getCompanyId(), List.of(request.getAppointmentId()));
        List<AppointmentPetScheduleSetting> schedules =
                petScheduleSettingService.listPetSchedules(request.getCompanyId(), List.of(request.getAppointmentId()));

        final var petScheduleDefMap = getPetScheduleDefMap(schedules, feedings, medications);

        responseObserver.onNext(GetPetFeedingMedicationSchedulesResponse.newBuilder()
                .addAllSchedules(petScheduleDefMap.entrySet().stream()
                        .map(entry -> entry.getValue().setPetId(entry.getKey()).build())
                        .toList())
                .addAllScheduleSettings(schedules.stream()
                        .map(scheduleSettingConverter::toModel)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    private Map<Long /*pet id*/, PetScheduleDef.Builder> getPetScheduleDefMap(
            List<AppointmentPetScheduleSetting> schedules,
            List<AppointmentPetFeeding> feedings,
            List<AppointmentPetMedication> medications) {
        Map<Integer, Map<Long, List<AppointmentPetScheduleSetting>>> scheduleMap = schedules.stream()
                .collect(Collectors.groupingBy(
                        AppointmentPetScheduleSetting::getScheduleType,
                        Collectors.groupingBy(AppointmentPetScheduleSetting::getScheduleId)));

        Map<Long, PetScheduleDef.Builder> petScheduleDefMap = new HashMap<>();
        feedings.forEach(feeding -> petScheduleDefMap
                .computeIfAbsent(feeding.getPetId(), k -> PetScheduleDef.newBuilder())
                .addFeedings(feedingConverter.toDef(feeding).toBuilder()
                        .addAllFeedingTimes(scheduleMap
                                .getOrDefault(BusinessPetScheduleType.FEEDING_VALUE, Map.of())
                                .getOrDefault(feeding.getId(), List.of())
                                .stream()
                                .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                        .setScheduleTime(time.getScheduleTime())
                                        .putAllExtraJson(
                                                JsonUtil.toBean(time.getScheduleExtraJson(), new TypeRef<>() {}))
                                        .build())
                                .toList())
                        .build()));
        medications.forEach(medication -> petScheduleDefMap
                .computeIfAbsent(medication.getPetId(), k -> PetScheduleDef.newBuilder())
                .addMedications(medicationConverter.toDef(medication).toBuilder()
                        .addAllMedicationTimes(scheduleMap
                                .getOrDefault(BusinessPetScheduleType.MEDICATION_VALUE, Map.of())
                                .getOrDefault(medication.getId(), List.of())
                                .stream()
                                .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                        .setScheduleTime(time.getScheduleTime())
                                        .putAllExtraJson(
                                                JsonUtil.toBean(time.getScheduleExtraJson(), new TypeRef<>() {}))
                                        .build())
                                .toList())
                        .build()));
        return petScheduleDefMap;
    }

    @Override
    public void batchGetPetFeedingMedicationSchedules(
            BatchGetPetFeedingMedicationSchedulesRequest request,
            StreamObserver<BatchGetPetFeedingMedicationSchedulesResponse> responseObserver) {
        if (CollectionUtils.isEmpty(request.getAppointmentIdsList())) {
            responseObserver.onNext(BatchGetPetFeedingMedicationSchedulesResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        List<Long> appointmentIds =
                request.getAppointmentIdsList().stream().distinct().toList();
        List<AppointmentPetFeeding> feedings =
                petFeedingService.listPetFeedings(request.getCompanyId(), appointmentIds);
        List<AppointmentPetMedication> medications =
                petMedicationService.listPetMedications(request.getCompanyId(), appointmentIds);
        List<AppointmentPetScheduleSetting> schedules =
                petScheduleSettingService.listPetSchedules(request.getCompanyId(), appointmentIds);
        var appointmentToFeedings =
                feedings.stream().collect(Collectors.groupingBy(AppointmentPetFeeding::getAppointmentId));
        var appointmentToMedications =
                medications.stream().collect(Collectors.groupingBy(AppointmentPetMedication::getAppointmentId));
        var appointmentToSchedules =
                schedules.stream().collect(Collectors.groupingBy(AppointmentPetScheduleSetting::getAppointmentId));
        var appointmentPetScheduleDefMap = new HashMap<Long, AppointmentPetScheduleDef>();
        appointmentIds.forEach(appointmentId -> {
            var petScheduleDefMap = getPetScheduleDefMap(
                    appointmentToSchedules.getOrDefault(appointmentId, List.of()),
                    appointmentToFeedings.getOrDefault(appointmentId, List.of()),
                    appointmentToMedications.getOrDefault(appointmentId, List.of()));
            appointmentPetScheduleDefMap.put(
                    appointmentId,
                    AppointmentPetScheduleDef.newBuilder()
                            .addAllSchedules(petScheduleDefMap.entrySet().stream()
                                    .map(entry -> entry.getValue()
                                            .setPetId(entry.getKey())
                                            .build())
                                    .toList())
                            .addAllScheduleSettings(
                                    appointmentToSchedules.getOrDefault(appointmentId, List.of()).stream()
                                            .map(scheduleSettingConverter::toModel)
                                            .toList())
                            .build());
        });
        responseObserver.onNext(BatchGetPetFeedingMedicationSchedulesResponse.newBuilder()
                .putAllAppointmentPetSchedules(appointmentPetScheduleDefMap)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void reschedulePetFeedingMedication(
            ReschedulePetFeedingMedicationRequest request,
            StreamObserver<ReschedulePetFeedingMedicationResponse> responseObserver) {
        petScheduleService.reschedulePetFeedingMedication(
                request.getCompanyId(), request.getAppointmentId(), request.getSchedulesList());

        // 支持 customer 修改。先不记 activity log
        if (request.getStaffId() > 0) {
            ActivityLogRecorder.record(
                    request.getBusinessId(),
                    request.getStaffId(),
                    AppointmentAction.RESCHEDULE_FEEDING_MEDICATION,
                    ResourceType.APPOINTMENT,
                    request.getAppointmentId(),
                    request.getSchedulesList());
        }

        publisher.publishEvent(new ReschedulePetFeedingMedicationEvent(this)
                .setCompanyId(request.getCompanyId())
                .setAppointmentId(request.getAppointmentId())
                .setPetSchedules(request.getSchedulesList()));

        responseObserver.onNext(ReschedulePetFeedingMedicationResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void calculateAppointmentSchedule(
            CalculateAppointmentScheduleRequest request,
            StreamObserver<CalculateAppointmentScheduleResponse> responseObserver) {
        List<PetDetailDef> petDetailDefs =
                PetDetailConverter.INSTANCE.toPetDetailDef(request.getPetServiceSchedulesList());
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap =
                offeringRemoteService.listService(request.getCompanyId(), request.getBusinessId(), petDetailDefs);
        List<PetDetailDTO> detailDTOList;
        if (isGroomingOnly(request.getPetServiceSchedulesList(), petServiceMap)) {
            GroomingOnlyDTO groomingOnlyDTO = PetDetailUtil.buildGroomingOnlyDTO(
                    request.getPetServiceSchedulesList(),
                    request.getAppointment().getAllPetsStartAtSameTime());
            detailDTOList =
                    petDetailService.buildGroomingOnlyPetDetailList(petDetailDefs, petServiceMap, groomingOnlyDTO);
        } else {
            detailDTOList = petDetailService.buildAllInOnePetDetailList(
                    request.getCompanyId(), request.getBusinessId(), petDetailDefs, petServiceMap, Map.of());
        }
        List<MoeGroomingPetDetail> petDetails =
                detailDTOList.stream().map(PetDetailDTO::getPetDetail).toList();
        List<PetServiceScheduleDef> petServiceSchedules =
                petDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId)).entrySet().stream()
                        .map(entry -> {
                            List<ServiceScheduleDef> serviceSchedules = entry.getValue().stream()
                                    .filter(petDetail ->
                                            Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_VALUE))
                                    .map(PetDetailConverter.INSTANCE::toServiceScheduleDef)
                                    .toList();
                            List<AddOnScheduleDef> addOnSchedules = entry.getValue().stream()
                                    .filter(petDetail ->
                                            Objects.equals(petDetail.getServiceType(), ServiceType.ADDON_VALUE))
                                    .map(PetDetailConverter.INSTANCE::toAddOnScheduleDef)
                                    .toList();
                            return PetServiceScheduleDef.newBuilder()
                                    .setPetId(entry.getKey())
                                    .addAllServiceSchedules(serviceSchedules)
                                    .addAllAddOnSchedules(addOnSchedules)
                                    .build();
                        })
                        // sort pet by the earliest schedule
                        .sorted((p1, p2) -> Stream.concat(
                                        p1.getServiceSchedulesList().stream()
                                                .map(s1 -> buildDateTime(s1.getStartDate(), s1.getStartTime())),
                                        p1.getAddOnSchedulesList().stream()
                                                .map(s2 -> buildDateTime(s2.getStartDate(), s2.getStartTime())))
                                .min(LocalDateTime::compareTo)
                                .orElseThrow()
                                .compareTo(Stream.concat(
                                                p2.getServiceSchedulesList().stream()
                                                        .map(s1 -> buildDateTime(s1.getStartDate(), s1.getStartTime())),
                                                p2.getAddOnSchedulesList().stream()
                                                        .map(s2 -> buildDateTime(s2.getStartDate(), s2.getStartTime())))
                                        .min(LocalDateTime::compareTo)
                                        .orElseThrow()))
                        .toList();
        Pair<LocalDateTime, LocalDateTime> period =
                petDetailService.calculatePeriod(request.getCompanyId(), petDetails, List.of());
        responseObserver.onNext(CalculateAppointmentScheduleResponse.newBuilder()
                .setAppointmentSchedule(AppointmentScheduleDef.newBuilder()
                        .setStartDate(period.first().toLocalDate().toString())
                        .setStartTime(period.first().toLocalTime().toSecondOfDay() / 60)
                        .setEndDate(period.second().toLocalDate().toString())
                        .setEndTime(period.second().toLocalTime().toSecondOfDay() / 60)
                        .build())
                .addAllPetServiceSchedules(petServiceSchedules)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchRescheduleAppointment(
            BatchRescheduleAppointmentRequest request,
            StreamObserver<BatchRescheduleAppointmentResponse> responseObserver) {

        List<MoeGroomingAppointment> appointments =
                appointmentService.getAppointments(request.getCompanyId(), request.getAppointmentIdsList());
        Map<Long, List<MoeGroomingPetDetail>> petDetailsByAppointmentIds =
                petDetailService.getPetDetailsByAppointmentIds(request.getAppointmentIdsList());
        Map<Long, List<MoeGroomingServiceOperation>> serviceOperationMapByAppointmentIds =
                serviceOperationService.getServiceOperationMapByAppointmentIds(request.getAppointmentIdsList());

        Map<Long, RescheduleGroomingDTO> rescheduleGroomingDTOListByAppointmentId =
                buildRescheduleGroomingDTOListForAppointment(request, appointments);

        appointments.forEach(appointment -> {
            var appointmentId = appointment.getId().longValue();
            var dto = rescheduleGroomingDTOListByAppointmentId.get(
                    appointment.getId().longValue());
            var petDetails = petDetailsByAppointmentIds.get(appointmentId);
            var serviceOperations = serviceOperationMapByAppointmentIds.getOrDefault(appointmentId, List.of());

            String beforeDate = getStartDate(CalendarCardType.APPOINTMENT, appointmentId);
            appointmentRescheduleService.rescheduleCalendarCard(dto, appointment, petDetails, serviceOperations);
            String afterDate = getStartDate(CalendarCardType.APPOINTMENT, appointmentId);

            var event = new RescheduleGroomingServiceEvent(this)
                    .setCompanyId(request.getCompanyId())
                    .setBusinessId(appointment.getBusinessId().longValue())
                    .setCustomerId(appointment.getCustomerId().longValue())
                    .setAppointmentId(appointmentId)
                    .setStaffId(request.getTargetStaffId())
                    .setRepeatAppointmentModifyScope(RepeatAppointmentModifyScope.ONLY_THIS)
                    .setRescheduleGroomingDTO(dto)
                    .setOffsetDays(DateUtil.getDaysDiffByTwoDate(beforeDate, afterDate))
                    .setTokenStaffId(request.getRescheduleBy())
                    .setBeforeStaffIds(PetDetailUtil.getStaffIdList(petDetails))
                    .setBeforeAppointmentDateTime(
                            buildDateTime(appointment.getAppointmentDate(), appointment.getAppointmentStartTime()));

            publisher.publishEvent(event);
        });

        responseObserver.onNext(BatchRescheduleAppointmentResponse.newBuilder()
                .addAllAppointments(AppointmentConverter.INSTANCE.toModel(appointments))
                .build());
        responseObserver.onCompleted();
    }

    // build reschedule grooming dto list for appointment, only reschedule staff & date
    private Map<Long, RescheduleGroomingDTO> buildRescheduleGroomingDTOListForAppointment(
            BatchRescheduleAppointmentRequest request, List<MoeGroomingAppointment> appointments) {

        Map<Long, RescheduleGroomingDTO> rescheduleGroomingDTOListByAppointmentId = new HashMap<>();

        appointments.forEach(appointment -> {
            var appointmentId = appointment.getId();

            var builder = RescheduleGroomingDTO.builder();

            if (request.hasTargetStaffId()) {
                builder.batchRescheduleAppointment(RescheduleGroomingDTO.BatchRescheduleAppointmentDTO.builder()
                        .sourceStaffId((int) request.getSourceStaffId())
                        .targetStaffId((int) request.getTargetStaffId())
                        .build());
            }

            if (request.hasTargetDate()) {
                builder.startDate(DateConverter.INSTANCE
                        .fromGoogleDate(request.getTargetDate())
                        .toString());
            } else {
                builder.startDate(appointment.getAppointmentDate());
                builder.endDate(appointment.getAppointmentEndDate());
            }

            rescheduleGroomingDTOListByAppointmentId.put(
                    appointmentId.longValue(),
                    builder.companyId(request.getCompanyId())
                            .appointmentId(appointmentId.longValue())
                            .cardType(CalendarCardType.APPOINTMENT)
                            .startTime(appointment.getAppointmentStartTime())
                            .endTime(appointment.getAppointmentEndTime())
                            .build());
        });

        return rescheduleGroomingDTOListByAppointmentId;
    }

    private enum PreviewType {
        NEW_APPOINTMENT,
        EDIT_APPOINTMENT_SCHEDULE,
        EDIT_PET_AND_SERVICE,
        ADD_PET_AND_SERVICE,
        EDIT_SERVICE_STAFF
    }

    private static PreviewType getPreviewType(
            PreviewCalendarScheduleRequest request,
            int offsetAppointmentMinutes,
            List<MoeGroomingPetDetail> allPetDetails) {
        if (!request.hasAppointmentId()) {
            return PreviewType.NEW_APPOINTMENT;
        }

        if (isEditAppointmentSchedule(offsetAppointmentMinutes)) {
            return PreviewType.EDIT_APPOINTMENT_SCHEDULE;
        }

        // pet、service 没变，只修改了 staff
        var requestServiceCount = request.getPetServicesList().stream()
                .map(PetServiceCalendarDef::getGroomingServicesList)
                .mapToLong(List::size)
                .sum();
        if (requestServiceCount == allPetDetails.size()) {
            Map<Long /* pet id */, Map<Long /* service id */, GroomingServiceCalendarDef>> petServiceStaffMap =
                    new HashMap<>();
            request.getPetServicesList()
                    .forEach(petService -> petService.getGroomingServicesList().forEach(service -> petServiceStaffMap
                            .computeIfAbsent(petService.getPetId(), k -> new HashMap<>())
                            .put(service.getServiceId(), service)));
            var anyMatch = allPetDetails.stream().anyMatch(petDetail -> {
                var requestService = petServiceStaffMap
                        .getOrDefault(petDetail.getPetId().longValue(), Map.of())
                        .get(petDetail.getServiceId().longValue());
                if (Objects.isNull(requestService)) {
                    return false;
                }
                return !Objects.equals(
                                requestService.getStaffId(),
                                petDetail.getStaffId().longValue())
                        && Objects.equals(requestService.getServiceTime(), petDetail.getServiceTime())
                        && (!requestService.hasWorkMode()
                                || Objects.equals(requestService.getWorkMode().getNumber(), petDetail.getWorkMode()));
            });
            if (anyMatch) {
                return PreviewType.EDIT_SERVICE_STAFF;
            }
        }

        var existingPet = allPetDetails.stream()
                .anyMatch(p -> Objects.equals(
                        p.getPetId().longValue(),
                        request.getPetServicesList().get(0).getPetId()));
        var changePet = request.hasOriginalPetId();

        return existingPet || changePet ? PreviewType.EDIT_PET_AND_SERVICE : PreviewType.ADD_PET_AND_SERVICE;
    }

    private static boolean isEditAppointmentSchedule(int appointmentOffsetMinutes) {
        return appointmentOffsetMinutes != 0;
    }

    private int getOffsetAppointmentMinutes(PreviewCalendarScheduleRequest request) {
        if (!request.hasAppointmentId() || !request.hasAppointmentSchedule()) {
            return 0;
        }
        var appointment = appointmentService.mustGet(request.getAppointmentId());

        var oldDateTime = buildDateTime(appointment.getAppointmentDate(), appointment.getAppointmentStartTime());
        var newDateTime = buildDateTime(
                request.getAppointmentSchedule().getStartDate(),
                request.getAppointmentSchedule().getStartTime());

        return calculateOffsetMinute(oldDateTime, newDateTime);
    }

    @Override
    public void previewCalendarSchedule(
            PreviewCalendarScheduleRequest request, StreamObserver<PreviewCalendarScheduleResponse> responseObserver) {
        var offsetAppointmentMinutes = getOffsetAppointmentMinutes(request);
        var allPetDetails = getAllPetDetails(request);
        var allOperations = getAllOperations(request);

        var previewPair =
                switch (getPreviewType(request, offsetAppointmentMinutes, allPetDetails)) {
                    case NEW_APPOINTMENT -> previewNewAppointment(request);
                    case EDIT_APPOINTMENT_SCHEDULE -> previewEditAppointmentSchedule(
                            request, offsetAppointmentMinutes, allPetDetails, allOperations);
                    case EDIT_PET_AND_SERVICE -> previewEditPetAndService(request, allPetDetails, allOperations);
                    case ADD_PET_AND_SERVICE -> previewAddPetAndService(request, allPetDetails, allOperations);
                    case EDIT_SERVICE_STAFF -> previewReplaceStaffSchedule(request, allPetDetails, allOperations);
                };

        var previewPetDetails = previewPair.first();
        var previewOperations = previewPair.second();
        var dateTypeMap = getDateTypeMap(previewPetDetails);

        responseObserver.onNext(PreviewCalendarScheduleResponse.newBuilder()
                .setAppointment(buildAppointmentModel(request, previewPetDetails))
                .addAllPetDetails(PetDetailConverter.INSTANCE.toModel(previewPetDetails, dateTypeMap))
                .addAllOperations(GroomingServiceOperationConverter.INSTANCE.toModel(previewOperations))
                .build());
        responseObserver.onCompleted();
    }

    Pair<List<MoeGroomingPetDetail>, List<MoeGroomingServiceOperation>> previewNewAppointment(
            PreviewCalendarScheduleRequest request) {
        if (request.hasAppointmentId() || !request.hasAppointmentSchedule()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid preview new appointment request.");
        }

        var previewStartDateTime = buildDateTime(
                request.getAppointmentSchedule().getStartDate(),
                request.getAppointmentSchedule().getStartTime());

        return previewPetDetailsAndOperations(request, previewStartDateTime);
    }

    Pair<List<MoeGroomingPetDetail>, List<MoeGroomingServiceOperation>> previewEditAppointmentSchedule(
            PreviewCalendarScheduleRequest request,
            int offsetMinutes,
            List<MoeGroomingPetDetail> allPetDetails,
            List<MoeGroomingServiceOperation> allOperations) {
        validateRequest(request);

        PetDetailUtil.offsetPetDetailsAndOperations(allPetDetails, allOperations, offsetMinutes);

        replaceStaff(request, allPetDetails);

        return Pair.of(allPetDetails, allOperations);
    }

    Pair<List<MoeGroomingPetDetail>, List<MoeGroomingServiceOperation>> previewReplaceStaffSchedule(
            PreviewCalendarScheduleRequest request,
            List<MoeGroomingPetDetail> allPetDetails,
            List<MoeGroomingServiceOperation> allOperations) {
        validateRequest(request);

        replaceStaff(request, allPetDetails);

        return Pair.of(allPetDetails, allOperations);
    }

    private static void replaceStaff(
            final PreviewCalendarScheduleRequest request, final List<MoeGroomingPetDetail> allPetDetails) {
        // 替换 staff
        Map<Long /* pet id */, Map<Long /* service id */, Long /* staff id */>> petServiceStaffMap = new HashMap<>();
        request.getPetServicesList()
                .forEach(petService -> petService.getGroomingServicesList().forEach(service -> petServiceStaffMap
                        .computeIfAbsent(petService.getPetId(), k -> new HashMap<>())
                        .put(service.getServiceId(), service.getStaffId())));
        allPetDetails.forEach(petDetail -> {
            if (petServiceStaffMap.containsKey(petDetail.getPetId().longValue())
                    && petServiceStaffMap
                            .get(petDetail.getPetId().longValue())
                            .containsKey(petDetail.getServiceId().longValue())) {
                petDetail.setStaffId(petServiceStaffMap
                        .get(petDetail.getPetId().longValue())
                        .get(petDetail.getServiceId().longValue())
                        .intValue());
            }
        });
    }

    Pair<List<MoeGroomingPetDetail>, List<MoeGroomingServiceOperation>> previewEditPetAndService(
            PreviewCalendarScheduleRequest request,
            List<MoeGroomingPetDetail> allPetDetails,
            List<MoeGroomingServiceOperation> allOperations) {
        validateRequest(request);
        var previewPetId = getPreviewPetId(request);

        // 1. 获取当前预览 pet 的结束时间
        var previewBeforePetDetails = allPetDetails.stream()
                .filter(p -> Objects.equals(p.getPetId(), previewPetId))
                .toList();
        var previewBeforeEndDateTime = getMaxEndDateTime(previewBeforePetDetails);

        // 2. All pets start at the same time 时维持原本开始时间，如果是替换 pet 则继承原本 pet 的开始时间，否则接在前一个 pet 后边开始
        LocalDateTime previewStartDateTime;
        if (request.getAllPetsStartAtSameTime()) {
            previewStartDateTime = getMinStartDateTime(previewBeforePetDetails);
        } else if (request.hasOriginalPetId()) {
            previewStartDateTime = getMinStartDateTimeByPet(allPetDetails, request.getOriginalPetId());
        } else {
            previewStartDateTime = getPreviousPetEndDateTime(allPetDetails, previewPetId);
        }

        // 3. 预览当前 pet 的 service schedule，并获取预览后的结束时间
        var previewPair = previewPetDetailsAndOperations(request, previewStartDateTime);
        var previewAfterEndDateTime = getMaxEndDateTime(previewPair.first());

        // 4. 减少当前 pet 与上一只 pet 之间的 gap
        var beforePair = getPetDetailsAndOperations(allPetDetails, allOperations, previewPetId, false);
        PetDetailUtil.reducePreviewPetScheduleGap(
                previewPair.first(), previewPair.second(), beforePair.first(), beforePair.second());

        // 5. 同步偏移当前 pet 之后的 pet 保持 gap 不变
        var afterPair = getPetDetailsAndOperations(allPetDetails, allOperations, previewPetId, true);
        var offsetMinutes = calculateOffsetMinute(previewBeforeEndDateTime, previewAfterEndDateTime);
        if (offsetMinutes != 0) {
            offsetPetDetailsAndOperations(afterPair.first(), afterPair.second(), offsetMinutes);
        }

        return Pair.of(
                Stream.of(beforePair.first(), previewPair.first(), afterPair.first())
                        .flatMap(List::stream)
                        .toList(),
                Stream.of(beforePair.second(), previewPair.second(), afterPair.second())
                        .flatMap(List::stream)
                        .toList());
    }

    private static int getPreviewPetId(PreviewCalendarScheduleRequest request) {
        return (int)
                (request.hasOriginalPetId()
                        ? request.getOriginalPetId()
                        : request.getPetServicesList().get(0).getPetId());
    }

    private LocalDateTime getPreviousPetEndDateTime(List<MoeGroomingPetDetail> allPetDetails, Integer previewPetId) {
        // Sort the pet details by start time
        var sortedPetDetails = allPetDetails.stream()
                .sorted(Comparator.comparing(petDetail -> buildDateTime(
                        petDetail.getStartDate(), petDetail.getStartTime().intValue())))
                .toList();

        // Find the pet that comes immediately before the previewPetId
        MoeGroomingPetDetail previousPet = null;
        for (MoeGroomingPetDetail petDetail : sortedPetDetails) {
            if (Objects.equals(petDetail.getPetId(), previewPetId)) {
                break;
            }
            previousPet = petDetail;
        }

        // Return the maximum end time of the previous pet
        if (previousPet != null) {
            return buildDateTime(
                    previousPet.getEndDate(), previousPet.getEndTime().intValue());
        }

        return allPetDetails.stream()
                .filter(petDetail -> Objects.equals(petDetail.getPetId(), previewPetId))
                .map(petDetail -> buildDateTime(
                        petDetail.getStartDate(), petDetail.getStartTime().intValue()))
                .min(LocalDateTime::compareTo)
                .orElseThrow(() -> bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST));
    }

    private Pair<List<MoeGroomingPetDetail>, List<MoeGroomingServiceOperation>> getPetDetailsAndOperations(
            List<MoeGroomingPetDetail> allPetDetails,
            List<MoeGroomingServiceOperation> allOperations,
            int previewPetId,
            boolean isAfter) {

        var petToMinStartDateTime = getPetStartTimes(allPetDetails);
        var petDateTime = petToMinStartDateTime.get(previewPetId);

        var targetPetIds = petToMinStartDateTime.entrySet().stream()
                .filter(entry -> isAfter == entry.getValue().isAfter(petDateTime))
                .map(Map.Entry::getKey)
                .filter(id -> id != previewPetId)
                .collect(Collectors.toSet());

        return Pair.of(
                allPetDetails.stream()
                        .filter(p -> targetPetIds.contains(p.getPetId()))
                        .toList(),
                allOperations.stream()
                        .filter(o -> targetPetIds.contains(o.getPetId()))
                        .toList());
    }

    private Map<Integer, LocalDateTime> getPetStartTimes(List<MoeGroomingPetDetail> allPetDetails) {
        return allPetDetails.stream()
                .collect(Collectors.toMap(
                        MoeGroomingPetDetail::getPetId,
                        p -> buildDateTime(p.getStartDate(), p.getStartTime().intValue()),
                        (existing, replacement) -> existing.isBefore(replacement) ? existing : replacement));
    }

    Pair<List<MoeGroomingPetDetail>, List<MoeGroomingServiceOperation>> previewAddPetAndService(
            PreviewCalendarScheduleRequest request,
            List<MoeGroomingPetDetail> allPetDetails,
            List<MoeGroomingServiceOperation> allOperations) {
        validateRequest(request);

        var appointmentEndDateTime = getAppointmentEndDateTime(request);

        var previewPair = previewPetDetailsAndOperations(request, appointmentEndDateTime);
        var previewPetDetails = previewPair.first();
        var previewOperations = previewPair.second();

        // Reduce the gap
        PetDetailUtil.reducePreviewPetScheduleGap(previewPetDetails, previewOperations, allPetDetails, allOperations);

        return Pair.of(
                Stream.concat(allPetDetails.stream(), previewPetDetails.stream())
                        .toList(),
                Stream.concat(allOperations.stream(), previewPair.second().stream())
                        .toList());
    }

    private void validateRequest(PreviewCalendarScheduleRequest request) {
        if (!request.hasAppointmentId() || !request.hasAppointmentSchedule()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid preview existing appointment request.");
        }
    }

    private LocalDateTime getAppointmentEndDateTime(PreviewCalendarScheduleRequest request) {
        var appointment = appointmentService.mustGet(request.getAppointmentId());
        return request.getAllPetsStartAtSameTime()
                ? buildDateTime(appointment.getAppointmentDate(), appointment.getAppointmentStartTime())
                : buildDateTime(appointment.getAppointmentEndDate(), appointment.getAppointmentEndTime());
    }

    private Pair<List<MoeGroomingPetDetail>, List<MoeGroomingServiceOperation>> previewPetDetailsAndOperations(
            PreviewCalendarScheduleRequest request, LocalDateTime previewStartDateTime) {
        var petServiceMap = listPetServiceMap(request);

        var details = PetDetailUtil.buildGroomingOnlyPetDetails(
                request.getPetServicesList(), petServiceMap, previewStartDateTime, request.getAllPetsStartAtSameTime());

        assignPreviewPetDetailId(details);

        var petDetails = details.stream().map(PetDetailDTO::getPetDetail).toList();
        var operations = details.stream()
                .flatMap(detail ->
                        Optional.ofNullable(detail.getOperations()).stream().flatMap(List::stream))
                .toList();

        return Pair.of(petDetails, operations);
    }

    private static void assignPreviewPetDetailId(List<PetDetailDTO> details) {
        var idGenerator = new AtomicInteger();
        details.forEach(detail -> {
            int petDetailId = idGenerator.getAndIncrement();
            detail.getPetDetail().setId(petDetailId);
            Optional.ofNullable(detail.getOperations())
                    .ifPresent(operations -> operations.forEach(o -> o.setGroomingServiceId(petDetailId)));
        });
    }

    private AppointmentModel buildAppointmentModel(
            PreviewCalendarScheduleRequest request, List<MoeGroomingPetDetail> petDetails) {
        var builder = initializeBuilder(request);
        var appointmentPeriod = petDetailService.calculatePeriod(request.getCompanyId(), petDetails, List.of());

        return builder.setAppointmentDate(
                        appointmentPeriod.first().toLocalDate().toString())
                .setAppointmentEndDate(appointmentPeriod.second().toLocalDate().toString())
                .setAppointmentStartTime(appointmentPeriod.first().toLocalTime().toSecondOfDay() / 60)
                .setAppointmentEndTime(appointmentPeriod.second().toLocalTime().toSecondOfDay() / 60)
                .build();
    }

    private AppointmentModel.Builder initializeBuilder(PreviewCalendarScheduleRequest request) {
        if (request.hasAppointmentId()) {
            MoeGroomingAppointment appointment = appointmentService.mustGet(request.getAppointmentId());
            return AppointmentConverter.INSTANCE.toModel(appointment).toBuilder();
        } else {
            return AppointmentModel.newBuilder()
                    .setCompanyId(request.getCompanyId())
                    .setBusinessId(request.getBusinessId())
                    .setCustomerId(request.getCustomerId());
        }
    }

    private Map<Long, Map<Long, CustomizedServiceView>> listPetServiceMap(PreviewCalendarScheduleRequest request) {
        var petToServiceIds = request.getPetServicesList().stream()
                .collect(Collectors.toMap(PetServiceCalendarDef::getPetId, def -> def.getGroomingServicesList().stream()
                        .map(GroomingServiceCalendarDef::getServiceId)
                        .toList()));
        return offeringRemoteService.listService(request.getCompanyId(), request.getBusinessId(), petToServiceIds);
    }

    private List<MoeGroomingPetDetail> getAllPetDetails(PreviewCalendarScheduleRequest request) {
        if (request.hasAppointmentId()) {
            return petDetailService.getPetDetailList(request.getAppointmentId());
        }
        return List.of();
    }

    private List<MoeGroomingServiceOperation> getAllOperations(PreviewCalendarScheduleRequest request) {
        if (request.hasAppointmentId()) {
            return serviceOperationService.getServiceOperationList(request.getAppointmentId());
        }
        return List.of();
    }

    private static LocalDateTime getMaxEndDateTime(List<MoeGroomingPetDetail> petDetails) {
        return petDetails.stream()
                .map(p -> buildDateTime(p.getEndDate(), p.getEndTime().intValue()))
                .max(LocalDateTime::compareTo)
                .orElseThrow(() -> bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST));
    }

    private static LocalDateTime getMinStartDateTime(List<MoeGroomingPetDetail> petDetails) {
        return petDetails.stream()
                .map(p -> buildDateTime(p.getStartDate(), p.getStartTime().intValue()))
                .min(LocalDateTime::compareTo)
                .orElseThrow(() -> bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST));
    }

    private static LocalDateTime getMinStartDateTimeByPet(List<MoeGroomingPetDetail> petDetails, long petId) {
        var filteredPetDetails = petDetails.stream()
                .filter(p -> Objects.equals(p.getPetId().longValue(), petId))
                .toList();
        return getMinStartDateTime(filteredPetDetails);
    }

    @Override
    public void rescheduleCalendarCard(
            RescheduleCalendarCardRequest request, StreamObserver<RescheduleCalendarCardResponse> responseObserver) {
        checkCalendarCardDraggable(request);

        var rescheduleCalendarCardDTO = new RescheduleCalendarCardDTO()
                .setCardType(request.getCardType())
                .setOriginalStaffId(request.getOriginalStaffId())
                .setAffectedPetDetailIds(request.getPetDetailIdsList().stream()
                        .map(Long::intValue)
                        .collect(Collectors.toSet()))
                .setAppointmentId(request.getAppointmentId())
                .setMoveAllCards(request.getMoveAllCards())
                .setOffsetMinutes(calculateOffsetMinutes(request));
        if (request.hasUpdatedBy()) {
            rescheduleCalendarCardDTO.setUpdatedBy(request.getUpdatedBy());
        }
        if (request.hasStaffId()) {
            rescheduleCalendarCardDTO.setTargetStaffId(request.getStaffId());
        }
        if (request.hasEndTime()) {
            rescheduleCalendarCardDTO.setTargetEndTime(request.getEndTime());
        }
        if (request.hasStartTime()) {
            rescheduleCalendarCardDTO.setTargetStartTime(request.getStartTime());
        }

        appointmentRescheduleService.rescheduleCalendarCard(rescheduleCalendarCardDTO);

        ThreadPool.execute(() -> {
            var petDetails = petDetailService.getPetDetailList(request.getAppointmentId());
            var reschedulePetServicePairs = petDetails.stream()
                    .filter(p ->
                            rescheduleCalendarCardDTO.getAffectedPetDetailIds().contains(p.getId()))
                    .map(p -> Pair.of(p.getPetId(), p.getServiceId()))
                    .toList();

            repeatAppointmentService
                    .listRepeatAppointment(request.getAppointmentId(), request.getRepeatModifyType())
                    .forEach(repeatAppointment -> {
                        var repeatAppointmentId = repeatAppointment.getId().longValue();
                        // 替换 repeat appointment 中相同 pet/service card 对应的 pet detail id
                        var affectPetDetailIds = petDetailService.getPetDetailList(repeatAppointmentId).stream()
                                .filter(p ->
                                        reschedulePetServicePairs.contains(Pair.of(p.getPetId(), p.getServiceId())))
                                .map(MoeGroomingPetDetail::getId)
                                .collect(Collectors.toSet());

                        rescheduleCalendarCardDTO
                                .setAppointmentId(repeatAppointmentId)
                                .setAffectedPetDetailIds(affectPetDetailIds);

                        appointmentRescheduleService.rescheduleCalendarCard(rescheduleCalendarCardDTO);
                    });
        });

        responseObserver.onNext(RescheduleCalendarCardResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private int calculateOffsetMinutes(RescheduleCalendarCardRequest request) {
        return switch (request.getCardType()) {
            case APPOINTMENT, BLOCK, BOOKING_REQUEST -> {
                var appointment = appointmentService.mustGet(request.getAppointmentId());

                yield calculateOffsetMinute(
                        appointment.getAppointmentDate(),
                        appointment.getAppointmentStartTime(),
                        request.getStartDate(),
                        request.hasStartTime()
                                ? Integer.valueOf(request.getStartTime())
                                : appointment.getAppointmentStartTime());
            }
            case SERVICE, OPERATION, SERVICE_AND_OPERATION -> {
                var currentCardDateTime = getCurrentCardDateTime(request);
                var currentCardStartDate = currentCardDateTime.toLocalDate().toString();
                var currentCardStartTime = currentCardDateTime.toLocalTime().toSecondOfDay() / 60;

                yield calculateOffsetMinute(
                        currentCardStartDate,
                        currentCardStartTime,
                        request.getStartDate(),
                        request.hasStartTime() ? request.getStartTime() : currentCardStartTime);
            }
            default -> 0;
        };
    }

    private MoeGroomingPetDetail getEarliestPetDetail(Long appointmentId, Set<Integer> affectPetDetailIds) {
        return petDetailService.getPetDetailList(appointmentId).stream()
                .filter(p -> affectPetDetailIds.contains(p.getId()))
                .min(Comparator.comparing(MoeGroomingPetDetail::getStartDate)
                        .thenComparing(MoeGroomingPetDetail::getStartTime))
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST));
    }

    private LocalDateTime getCurrentCardDateTime(RescheduleCalendarCardRequest request) {
        var affectPetDetailIds =
                request.getPetDetailIdsList().stream().map(Long::intValue).collect(Collectors.toSet());

        var earliestPetDetail = getEarliestPetDetail(request.getAppointmentId(), affectPetDetailIds);

        return serviceOperationService.getServiceOperationList(request.getAppointmentId()).stream()
                .filter(o -> Objects.equals(earliestPetDetail.getId(), o.getGroomingServiceId())
                        && Objects.equals(
                                request.getOriginalStaffId(), o.getStaffId().longValue()))
                .min(Comparator.comparing(MoeGroomingServiceOperation::getStartTime))
                .map(o -> buildDateTime(earliestPetDetail.getStartDate(), o.getStartTime()))
                .orElse(buildDateTime(
                        earliestPetDetail.getStartDate(),
                        earliestPetDetail.getStartTime().intValue()));
    }

    /**
     * APPOINTMENT: <br>
     *  - 可以任意修改 staff/date/time <br>
     *  - 卡片上有一个非 0 min 的 service 和若干个 0 min service 允许修改 service duration <br>
     *  - 卡片上有且仅有一个 0 min 的 service 时也允许修改 service duration <br>
     * SERVICE: <br>
     *  - 可以任意修改 staff
     *  - 修改 date/time 范围不能超过 appointment 的 [date, endDate] <br>
     *  - 卡片上有一个非 0 min 的 service 和若干个 0 min service 允许修改 service duration <br>
     *  - 卡片上有且仅有一个 0 min 的 service 时也允许修改 service duration <br>
     * OPERATION/SERVICE_AND_OPERATION: <br>
     *  - 修改的 staff 不能是 service multi-staff 另外的 staff <br>
     *  - 修改 date/time 范围不能超过 appointment 的 [date, endDate] <br>
     *  - 不允许修改 service duration 长度 <br>
     * BLOCK: <br>
     *  - 可以任意修改 staff/date/time <br>
     * BOOKING_REQUEST: <br>
     * - 代码逻辑上允许拉伸，交互上不允许 <br>
     *
     * @param request       reschedule request
     */
    private void checkCalendarCardDraggable(RescheduleCalendarCardRequest request) {
        var appointment = appointmentService.mustGet(request.getAppointmentId());

        checkEndTimeOutOfRange(request);

        switch (request.getCardType()) {
            case APPOINTMENT -> {
                var allPetDetails = petDetailService.getPetDetailList(request.getAppointmentId());

                checkModifyMultipleServicesDuration(request, allPetDetails);
            }
            case SERVICE -> {
                checkPetDetailIds(request);

                checkOutOfRange(request.getStartDate(), appointment);

                var petDetails = petDetailService.batchGet(request.getPetDetailIdsList().stream()
                        .map(Long::intValue)
                        .toList());
                checkModifyMultipleServicesDuration(request, petDetails);
            }
            case OPERATION, SERVICE_AND_OPERATION -> {
                checkPetDetailIds(request);

                checkOutOfRange(request.getStartDate(), appointment);

                checkModifyServiceDuration(request);

                var allOperations = serviceOperationService.getServiceOperationList(request.getAppointmentId());

                checkModifyOperationStaff(request, allOperations);
            }
            case BOOKING_REQUEST -> throw bizException(
                    Code.CODE_PARAMS_ERROR, "Not allowed to reschedule pending request.");
            default -> {}
        }
    }

    private static void checkPetDetailIds(RescheduleCalendarCardRequest request) {
        if (request.getPetDetailIdsCount() == 0) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Pet detail ids is required.");
        }
    }

    /**
     * 校验 reschedule date 是否在 [startDate, endDate] 范围内
     *
     * @param requestDate reschedule date
     * @param appointment reschedule appointment
     */
    private static void checkOutOfRange(String requestDate, MoeGroomingAppointment appointment) {
        var existingStartDate = LocalDate.parse(appointment.getAppointmentDate());
        var existingEndDate = LocalDate.parse(appointment.getAppointmentEndDate());
        var requestStartDate = LocalDate.parse(requestDate);
        if (requestStartDate.isBefore(existingStartDate) || requestStartDate.isAfter(existingEndDate)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Multi-card cross-day dragging is not allowed.");
        }
    }

    private static void checkModifyOperationStaff(
            RescheduleCalendarCardRequest request, List<MoeGroomingServiceOperation> operations) {
        if (!request.hasStaffId()) {
            return;
        }
        var petDetailId = request.getPetDetailIdsList().get(0);
        var originalStaffId = request.getOriginalStaffId();
        var targetStaffId = request.getStaffId();
        var notAllowed = operations.stream()
                .filter(o -> Objects.equals(o.getGroomingServiceId(), petDetailId.intValue()))
                .map(MoeGroomingServiceOperation::getStaffId)
                .map(Integer::longValue)
                .filter(id -> !Objects.equals(id, originalStaffId))
                .anyMatch(id -> Objects.equals(id, targetStaffId));
        if (notAllowed) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Not allowed to reschedule to a different staff member within the same service.");
        }
    }

    private static void checkEndTimeOutOfRange(RescheduleCalendarCardRequest request) {
        if (request.hasEndTime() && request.getEndTime() > 1439) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Not allowed end time be greater than 1439.");
        }
    }

    private static void checkModifyServiceDuration(RescheduleCalendarCardRequest request) {
        if (request.hasEndTime()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Not allowed to modify service duration.");
        }
    }

    static void checkModifyMultipleServicesDuration(
            RescheduleCalendarCardRequest request, List<MoeGroomingPetDetail> modifyDurationPetDetails) {
        if (!request.hasEndTime()) {
            return;
        }
        var nonZeroDurationCount = modifyDurationPetDetails.stream()
                .filter(p -> p.getServiceTime() > 0)
                .count();
        var zeroDurationCount = modifyDurationPetDetails.stream()
                .filter(p -> p.getServiceTime() == 0)
                .count();
        if (nonZeroDurationCount > 1) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Not allowed to modify multiple services duration.");
        }
        if (nonZeroDurationCount == 0 && zeroDurationCount > 1) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Not allowed to modify multiple zero-duration services.");
        }
    }

    @Override
    public void switchAllPetsStartAtSameTime(
            SwitchAllPetsStartAtSameTimeRequest request,
            StreamObserver<SwitchAllPetsStartAtSameTimeResponse> responseObserver) {
        var appointment = appointmentService.mustGet(request.getAppointmentId());
        var petDetails = petDetailService.getPetDetailList(request.getAppointmentId());
        var operations = serviceOperationService.getServiceOperationList(request.getAppointmentId());
        var appointmentDateTime =
                buildDateTime(appointment.getAppointmentDate(), appointment.getAppointmentStartTime());

        var details = PetDetailUtil.buildGroomingOnlyPetDetails(
                petDetails, operations, appointmentDateTime, request.getAllPetsStartAtSameTime());

        var updatePetDetails = details.stream().map(PetDetailDTO::getPetDetail).toList();
        var updateOperations = details.stream()
                .map(PetDetailDTO::getOperations)
                .flatMap(List::stream)
                .toList();
        appointmentCompositeService.updateServiceDetails(updatePetDetails, updateOperations);

        responseObserver.onNext(SwitchAllPetsStartAtSameTimeResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void reschedulePetDetails(
            ReschedulePetDetailsRequest request, StreamObserver<ReschedulePetDetailsResponse> responseObserver) {
        var beforeAppointment = appointmentService.mustGet(request.getAppointmentId());
        var beforePetDetails = petDetailService.getPetDetailList(request.getAppointmentId());
        var updatedPetDetails = buildServiceSchedule(request.getPetDetailsList(), beforePetDetails);

        appointmentRescheduleService.reschedulePetDetails(beforeAppointment, beforePetDetails, updatedPetDetails);

        // Handle repeat daycare appointments asynchronously
        handleRepeatDaycareAppointments(request, beforeAppointment, beforePetDetails);

        responseObserver.onNext(ReschedulePetDetailsResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void reschedulePetFeeding(
            ReschedulePetFeedingRequest request, StreamObserver<ReschedulePetFeedingResponse> responseObserver) {
        petScheduleService.reschedulePetFeeding(
                request.getCompanyId(), request.getAppointmentId(), request.getFeedingsSchedulesList());

        ActivityLogRecorder.record(
                request.getBusinessId(),
                request.getStaffId(),
                AppointmentAction.RESCHEDULE_FEEDING,
                ResourceType.APPOINTMENT,
                request.getAppointmentId(),
                request.getFeedingsSchedulesList());

        List<PetScheduleDef> petScheduleDefs =
                getPetScheduleDefList(request.getCompanyId(), request.getAppointmentId());

        publisher.publishEvent(new ReschedulePetFeedingMedicationEvent(this)
                .setCompanyId(request.getCompanyId())
                .setAppointmentId(request.getAppointmentId())
                .setPetSchedules(petScheduleDefs));

        responseObserver.onNext(ReschedulePetFeedingResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void reschedulePetMedication(
            ReschedulePetMedicationRequest request, StreamObserver<ReschedulePetMedicationResponse> responseObserver) {
        petScheduleService.reschedulePetMedication(
                request.getCompanyId(), request.getAppointmentId(), request.getMedicationsSchedulesList());

        ActivityLogRecorder.record(
                request.getBusinessId(),
                request.getStaffId(),
                AppointmentAction.RESCHEDULE_MEDICATION,
                ResourceType.APPOINTMENT,
                request.getAppointmentId(),
                request.getMedicationsSchedulesList());

        List<PetScheduleDef> petScheduleDefs =
                getPetScheduleDefList(request.getCompanyId(), request.getAppointmentId());

        publisher.publishEvent(new ReschedulePetFeedingMedicationEvent(this)
                .setCompanyId(request.getCompanyId())
                .setAppointmentId(request.getAppointmentId())
                .setPetSchedules(petScheduleDefs));

        responseObserver.onNext(ReschedulePetMedicationResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private List<PetScheduleDef> getPetScheduleDefList(long companyId, long appointmentId) {
        List<AppointmentPetFeeding> feedings = petFeedingService.listPetFeedings(companyId, List.of(appointmentId));
        List<AppointmentPetMedication> medications =
                petMedicationService.listPetMedications(companyId, List.of(appointmentId));
        List<AppointmentPetScheduleSetting> schedules =
                petScheduleSettingService.listPetSchedules(companyId, List.of(appointmentId));
        var petScheduleDefMap = getPetScheduleDefMap(schedules, feedings, medications);

        return petScheduleDefMap.entrySet().stream()
                .map(entry -> entry.getValue().setPetId(entry.getKey()).build())
                .toList();
    }

    static List<MoeGroomingPetDetail> buildServiceSchedule(
            List<PetDetailScheduleDef> scheduleDefs, List<MoeGroomingPetDetail> beforePetDetails) {
        var idToPetDetail =
                beforePetDetails.stream().collect(Collectors.toMap(MoeGroomingPetDetail::getId, Function.identity()));

        var appointmentStartDate = getAppointmentStartDate(scheduleDefs);
        var appointmentEndDate = getAppointmentEndDate(scheduleDefs);

        return scheduleDefs.stream()
                .map(def -> {
                    var before = idToPetDetail.get(Math.toIntExact(def.getId()));
                    if (before == null) {
                        throw bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST);
                    }
                    if (Objects.equals(before.getServiceType(), ServiceType.SERVICE_VALUE)) {
                        return buildServiceSchedule(appointmentStartDate, appointmentEndDate, before, def);
                    } else {
                        return buildScheduleWithDateType(
                                appointmentStartDate, appointmentEndDate, def, before.getServiceTime());
                    }
                })
                .toList();
    }

    private static String getAppointmentStartDate(List<PetDetailScheduleDef> scheduleDefs) {
        return scheduleDefs.stream()
                .filter(def -> def.hasStartDate() && !def.getStartDate().isEmpty())
                .map(PetDetailScheduleDef::getStartDate)
                .min(Comparator.naturalOrder())
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Appointment start date is required."));
    }

    private static String getAppointmentEndDate(List<PetDetailScheduleDef> scheduleDefs) {
        return scheduleDefs.stream()
                .filter(def -> def.hasEndDate() && !def.getEndDate().isEmpty())
                .map(PetDetailScheduleDef::getEndDate)
                .max(Comparator.naturalOrder())
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Appointment end date is required."));
    }

    private void handleRepeatDaycareAppointments(
            ReschedulePetDetailsRequest request,
            MoeGroomingAppointment beforeAppointment,
            List<MoeGroomingPetDetail> beforePetDetails) {
        if (!isRepeatDaycare(beforeAppointment)) {
            return;
        }

        var idToPetDetail =
                beforePetDetails.stream().collect(Collectors.toMap(MoeGroomingPetDetail::getId, Function.identity()));
        var offsetDays = getOffsetDays(beforeAppointment);
        ThreadPool.execute(() -> repeatAppointmentService
                .listRepeatAppointment(request.getAppointmentId(), request.getRepeatAppointmentModifyScope())
                .forEach(repeatAppointment ->
                        updateRepeatAppointment(request, repeatAppointment, idToPetDetail, offsetDays)));
    }

    private void updateRepeatAppointment(
            ReschedulePetDetailsRequest request,
            MoeGroomingAppointment repeatAppointment,
            Map<Integer, MoeGroomingPetDetail> idToPetDetail,
            long offsetDays) {
        var appointmentStartDate = LocalDate.parse(repeatAppointment.getAppointmentDate())
                .plusDays(offsetDays)
                .toString();
        var appointmentEndDate = LocalDate.parse(repeatAppointment.getAppointmentEndDate())
                .plusDays(offsetDays)
                .toString();
        var repeatPetDetails =
                petDetailService.getPetDetailList(repeatAppointment.getId().longValue());
        var keyToPetDetail = getUniqueKeyToPetDetail(repeatPetDetails);

        var repeatUpdatedPetDetails = request.getPetDetailsList().stream()
                .map(def -> {
                    var editPetDetail = idToPetDetail.get(Math.toIntExact(def.getId()));
                    if (editPetDetail == null) {
                        throw bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST);
                    }
                    var repeatPetDetail = keyToPetDetail.get(PetDetailUtil.buildPetDetailKey(editPetDetail));
                    if (repeatPetDetail == null) {
                        return null;
                    }
                    var repeatDef = buildRepeat(def, repeatPetDetail, offsetDays);
                    if (Objects.equals(repeatPetDetail.getServiceType(), ServiceType.SERVICE_VALUE)) {
                        return buildServiceSchedule(
                                appointmentStartDate, appointmentEndDate, repeatPetDetail, repeatDef);
                    } else {
                        return buildScheduleWithDateType(
                                appointmentStartDate, appointmentEndDate, repeatDef, repeatPetDetail.getServiceTime());
                    }
                })
                .filter(Objects::nonNull)
                .toList();

        appointmentRescheduleService.reschedulePetDetails(repeatAppointment, repeatPetDetails, repeatUpdatedPetDetails);
    }

    private Map<String, MoeGroomingPetDetail> getUniqueKeyToPetDetail(List<MoeGroomingPetDetail> repeatPetDetails) {
        return repeatPetDetails.stream()
                .collect(Collectors.groupingBy(PetDetailUtil::buildPetDetailKey))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().size() == 1)
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> entry.getValue().get(0)));
    }

    private static boolean isRepeatDaycare(MoeGroomingAppointment appointment) {
        return Objects.equals(
                ServiceItemEnum.DAYCARE, ServiceItemEnum.getMainServiceItemType(appointment.getServiceTypeInclude()));
    }

    private long getOffsetDays(MoeGroomingAppointment before) {
        var after = appointmentService.mustGet(before.getId());
        return DateUtil.getDaysDiffByTwoDate(before.getAppointmentDate(), after.getAppointmentDate());
    }

    private static PetDetailScheduleDef buildRepeat(
            PetDetailScheduleDef def, MoeGroomingPetDetail repeatPetDetail, long offsetDays) {
        var builder = def.toBuilder().setId(repeatPetDetail.getId());
        if (def.hasStartDate()) {
            builder.setStartDate(LocalDate.parse(repeatPetDetail.getStartDate())
                    .plusDays(offsetDays)
                    .toString());
        }
        if (def.hasEndDate()) {
            builder.setEndDate(LocalDate.parse(repeatPetDetail.getEndDate())
                    .plusDays(offsetDays)
                    .toString());
        }
        return builder.build();
    }

    private static MoeGroomingPetDetail buildServiceSchedule(
            String appointmentStartDate,
            String appointmentEndDate,
            MoeGroomingPetDetail before,
            PetDetailScheduleDef def) {
        return switch (Objects.requireNonNull(ServiceItemType.forNumber(before.getServiceItemType()))) {
            case BOARDING -> buildBoardingSchedule(def);
            case DAYCARE -> buildDaycareSchedule(appointmentStartDate, appointmentEndDate, def);
            case GROOMING -> buildGroomingSchedule(
                    appointmentStartDate, appointmentEndDate, def, before.getServiceTime());
            case DOG_WALKING -> buildDogWalkingSchedule(def, before.getServiceTime());
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown service item type");
        };
    }

    static MoeGroomingPetDetail buildBoardingSchedule(PetDetailScheduleDef def) {
        MoeGroomingPetDetail updated = new MoeGroomingPetDetail();
        updated.setId((int) def.getId());
        updated.setStartDate(def.getStartDate());
        updated.setStartTime((long) def.getStartTime());
        updated.setEndDate(def.getEndDate());
        updated.setEndTime((long) def.getEndTime());
        updated.setServiceTime(
                calculateOffsetMinute(def.getStartDate(), def.getStartTime(), def.getEndDate(), def.getEndTime()));
        return updated;
    }

    static MoeGroomingPetDetail buildDaycareSchedule(
            String appointmentStartDate, String appointmentEndDate, PetDetailScheduleDef def) {
        // Daycare can update start_time and end_time
        int updatedServiceTime = def.getEndTime() - def.getStartTime();

        MoeGroomingPetDetail updated =
                buildScheduleWithDateType(appointmentStartDate, appointmentEndDate, def, updatedServiceTime);

        updated.setStartTime((long) def.getStartTime());
        updated.setEndTime((long) def.getEndTime());
        updated.setServiceTime(updatedServiceTime);
        return updated;
    }

    static MoeGroomingPetDetail buildDogWalkingSchedule(PetDetailScheduleDef def, int serviceTime) {
        return buildDatePointSchedule(def, serviceTime);
    }

    static MoeGroomingPetDetail buildGroomingSchedule(
            String appointmentStartDate, String appointmentEndDate, PetDetailScheduleDef def, int serviceTime) {
        return switch (def.getDateType()) {
            case PET_DETAIL_DATE_FIRST_DAY, PET_DETAIL_DATE_LAST_DAY -> buildSingleDaySchedule(
                    appointmentStartDate, appointmentEndDate, def, serviceTime);
            default -> buildDatePointSchedule(def, serviceTime);
        };
    }

    static MoeGroomingPetDetail buildScheduleWithDateType(
            String appointmentStartDate, String appointmentEndDate, PetDetailScheduleDef def, int serviceTime) {
        if (!def.hasDateType()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Date type is required.");
        }
        return switch (def.getDateType()) {
            case PET_DETAIL_DATE_EVERYDAY,
                    PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
                    PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> buildEverydaySchedule(def);
            case PET_DETAIL_DATE_SPECIFIC_DATE -> buildSpecificDatesSchedule(def);
            case PET_DETAIL_DATE_DATE_POINT -> buildDatePointSchedule(def, serviceTime);
            case PET_DETAIL_DATE_LAST_DAY, PET_DETAIL_DATE_FIRST_DAY -> buildSingleDaySchedule(
                    appointmentStartDate, appointmentEndDate, def, serviceTime);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown service item type");
        };
    }

    static MoeGroomingPetDetail buildSingleDaySchedule(
            String appointmentStartDate, String appointmentEndDate, PetDetailScheduleDef def, int serviceTime) {
        var date =
                switch (def.getDateType()) {
                    case PET_DETAIL_DATE_FIRST_DAY -> appointmentStartDate;
                    case PET_DETAIL_DATE_LAST_DAY -> appointmentEndDate;
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown date type");
                };
        MoeGroomingPetDetail updated = new MoeGroomingPetDetail();
        updated.setId((int) def.getId());
        updated.setStartDate(date);
        updated.setStartTime((long) def.getStartTime());
        var endDateTime = buildDateTime(date, def.getStartTime()).plusMinutes(serviceTime);
        updated.setEndDate(endDateTime.toLocalDate().toString());
        updated.setEndTime((long) endDateTime.toLocalTime().toSecondOfDay() / 60);
        updated.setSpecificDates(JsonUtil.toJson(List.of()));
        updated.setDateType(def.getDateTypeValue());
        return updated;
    }

    static MoeGroomingPetDetail buildEverydaySchedule(PetDetailScheduleDef def) {
        MoeGroomingPetDetail updated = buildNonStaffSchedule(def);

        updated.setSpecificDates(JsonUtil.toJson(List.of()));

        return updated;
    }

    static MoeGroomingPetDetail buildSpecificDatesSchedule(PetDetailScheduleDef def) {
        MoeGroomingPetDetail updated = buildNonStaffSchedule(def);

        updated.setSpecificDates(JsonUtil.toJson(def.getSpecificDatesList()));

        return updated;
    }

    private static MoeGroomingPetDetail buildNonStaffSchedule(PetDetailScheduleDef def) {
        MoeGroomingPetDetail updated = new MoeGroomingPetDetail();
        updated.setId((int) def.getId());
        updated.setStartDate("");
        updated.setStartTime(0L);
        updated.setEndDate("");
        updated.setEndTime(0L);
        updated.setStaffId(0);
        updated.setDateType(def.getDateTypeValue());
        updated.setQuantityPerDay(getQuantityPerDay(def));
        return updated;
    }

    static MoeGroomingPetDetail buildDatePointSchedule(PetDetailScheduleDef def, int serviceTime) {
        MoeGroomingPetDetail updated = new MoeGroomingPetDetail();
        updated.setId((int) def.getId());
        updated.setStartDate(def.getStartDate());
        updated.setStartTime((long) def.getStartTime());
        var endDateTime = buildDateTime(def.getStartDate(), def.getStartTime()).plusMinutes(serviceTime);
        updated.setEndDate(endDateTime.toLocalDate().toString());
        updated.setEndTime((long) endDateTime.toLocalTime().toSecondOfDay() / 60);
        updated.setSpecificDates(JsonUtil.toJson(List.of()));
        return updated;
    }

    private static int getQuantityPerDay(PetDetailScheduleDef def) {
        return def.hasQuantityPerDay() ? def.getQuantityPerDay() : 1;
    }
}
