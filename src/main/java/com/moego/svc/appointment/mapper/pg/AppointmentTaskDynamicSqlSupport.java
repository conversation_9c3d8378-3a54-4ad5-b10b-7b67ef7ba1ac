package com.moego.svc.appointment.mapper.pg;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AppointmentTaskDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    public static final AppointmentTask appointmentTask = new AppointmentTask();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.id")
    public static final SqlColumn<Long> id = appointmentTask.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.company_id")
    public static final SqlColumn<Long> companyId = appointmentTask.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.business_id")
    public static final SqlColumn<Long> businessId = appointmentTask.businessId;

    /**
     * Database Column Remarks:
     *   feeding/medication/add_on/pee/poo/custom...
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.task_category")
    public static final SqlColumn<String> taskCategory = appointmentTask.taskCategory;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.instruction")
    public static final SqlColumn<String> instruction = appointmentTask.instruction;

    /**
     * Database Column Remarks:
     *   optional, moe_grooming_appointment.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.appointment_id")
    public static final SqlColumn<Long> appointmentId = appointmentTask.appointmentId;

    /**
     * Database Column Remarks:
     *   optional, main service, moe_grooming_service.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.service_id")
    public static final SqlColumn<Long> serviceId = appointmentTask.serviceId;

    /**
     * Database Column Remarks:
     *   optional, related service item type, 1-grooming, 2-boarding, 3-daycare, 4-evaluation
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.care_type")
    public static final SqlColumn<Integer> careType = appointmentTask.careType;

    /**
     * Database Column Remarks:
     *   optional, moe_customer_pet.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.pet_id")
    public static final SqlColumn<Long> petId = appointmentTask.petId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.lodging_id")
    public static final SqlColumn<Long> lodgingId = appointmentTask.lodgingId;

    /**
     * Database Column Remarks:
     *   optional, moe_staff.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.staff_id")
    public static final SqlColumn<Long> staffId = appointmentTask.staffId;

    /**
     * Database Column Remarks:
     *   feeding/medication date or add-on date
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.start_date")
    public static final SqlColumn<LocalDate> startDate = appointmentTask.startDate;

    /**
     * Database Column Remarks:
     *   feeding/medication time, field is null when it is add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.start_time")
    public static final SqlColumn<Integer> startTime = appointmentTask.startTime;

    /**
     * Database Column Remarks:
     *   feeding/medication time label, example 09:00 is AM
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.time_label")
    public static final SqlColumn<String> timeLabel = appointmentTask.timeLabel;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.duration")
    public static final SqlColumn<Integer> duration = appointmentTask.duration;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.status")
    public static final SqlColumn<String> status = appointmentTask.status;

    /**
     * Database Column Remarks:
     *   record status information on the completion of various tasks
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_status")
    public static final SqlColumn<String> noteStatus = appointmentTask.noteStatus;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_content")
    public static final SqlColumn<String> noteContent = appointmentTask.noteContent;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = appointmentTask.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = appointmentTask.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = appointmentTask.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.add_on_id")
    public static final SqlColumn<Long> addOnId = appointmentTask.addOnId;

    /**
     * Database Column Remarks:
     *   feeding/medication note feedback
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_feedback")
    public static final SqlColumn<String> noteFeedback = appointmentTask.noteFeedback;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    public static final class AppointmentTask extends AliasableSqlTable<AppointmentTask> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<String> taskCategory = column("task_category", JDBCType.VARCHAR);

        public final SqlColumn<String> instruction = column("instruction", JDBCType.VARCHAR);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> careType = column("care_type", JDBCType.INTEGER);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> lodgingId = column("lodging_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<LocalDate> startDate = column("start_date", JDBCType.DATE);

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<String> timeLabel = column("time_label", JDBCType.VARCHAR);

        public final SqlColumn<Integer> duration = column("duration", JDBCType.INTEGER);

        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);

        public final SqlColumn<String> noteStatus = column("note_status", JDBCType.VARCHAR);

        public final SqlColumn<String> noteContent = column("note_content", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> addOnId = column("add_on_id", JDBCType.BIGINT);

        public final SqlColumn<String> noteFeedback = column("note_feedback", JDBCType.VARCHAR);

        public AppointmentTask() {
            super("appointment_task", AppointmentTask::new);
        }
    }
}