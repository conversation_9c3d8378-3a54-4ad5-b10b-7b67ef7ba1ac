package com.moego.svc.appointment.mapper.pg;

import static com.moego.svc.appointment.mapper.pg.AppointmentTaskDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.AppointmentTask;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface AppointmentTaskMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<AppointmentTaskMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, businessId, taskCategory, instruction, appointmentId, serviceId, careType, petId, lodgingId, staffId, startDate, startTime, timeLabel, duration, status, noteStatus, noteContent, createdAt, updatedAt, deletedAt, addOnId, noteFeedback);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<AppointmentTask> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<AppointmentTask> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AppointmentTaskResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="task_category", property="taskCategory", jdbcType=JdbcType.VARCHAR),
        @Result(column="instruction", property="instruction", jdbcType=JdbcType.VARCHAR),
        @Result(column="appointment_id", property="appointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="care_type", property="careType", jdbcType=JdbcType.INTEGER),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="lodging_id", property="lodgingId", jdbcType=JdbcType.BIGINT),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="time_label", property="timeLabel", jdbcType=JdbcType.VARCHAR),
        @Result(column="duration", property="duration", jdbcType=JdbcType.INTEGER),
        @Result(column="status", property="status", jdbcType=JdbcType.VARCHAR),
        @Result(column="note_status", property="noteStatus", jdbcType=JdbcType.VARCHAR),
        @Result(column="note_content", property="noteContent", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="add_on_id", property="addOnId", jdbcType=JdbcType.BIGINT),
        @Result(column="note_feedback", property="noteFeedback", jdbcType=JdbcType.VARCHAR)
    })
    List<AppointmentTask> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AppointmentTaskResult")
    Optional<AppointmentTask> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, appointmentTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, appointmentTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default int insertMultiple(Collection<AppointmentTask> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, appointmentTask, c ->
            c.map(companyId).toProperty("companyId")
            .map(businessId).toProperty("businessId")
            .map(taskCategory).toProperty("taskCategory")
            .map(instruction).toProperty("instruction")
            .map(appointmentId).toProperty("appointmentId")
            .map(serviceId).toProperty("serviceId")
            .map(careType).toProperty("careType")
            .map(petId).toProperty("petId")
            .map(lodgingId).toProperty("lodgingId")
            .map(staffId).toProperty("staffId")
            .map(startDate).toProperty("startDate")
            .map(startTime).toProperty("startTime")
            .map(timeLabel).toProperty("timeLabel")
            .map(duration).toProperty("duration")
            .map(status).toProperty("status")
            .map(noteStatus).toProperty("noteStatus")
            .map(noteContent).toProperty("noteContent")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(addOnId).toProperty("addOnId")
            .map(noteFeedback).toProperty("noteFeedback")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default int insertSelective(AppointmentTask row) {
        return MyBatis3Utils.insert(this::insert, row, appointmentTask, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(taskCategory).toPropertyWhenPresent("taskCategory", row::getTaskCategory)
            .map(instruction).toPropertyWhenPresent("instruction", row::getInstruction)
            .map(appointmentId).toPropertyWhenPresent("appointmentId", row::getAppointmentId)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
            .map(careType).toPropertyWhenPresent("careType", row::getCareType)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(lodgingId).toPropertyWhenPresent("lodgingId", row::getLodgingId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(timeLabel).toPropertyWhenPresent("timeLabel", row::getTimeLabel)
            .map(duration).toPropertyWhenPresent("duration", row::getDuration)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(noteStatus).toPropertyWhenPresent("noteStatus", row::getNoteStatus)
            .map(noteContent).toPropertyWhenPresent("noteContent", row::getNoteContent)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(addOnId).toPropertyWhenPresent("addOnId", row::getAddOnId)
            .map(noteFeedback).toPropertyWhenPresent("noteFeedback", row::getNoteFeedback)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default Optional<AppointmentTask> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, appointmentTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default List<AppointmentTask> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, appointmentTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default List<AppointmentTask> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, appointmentTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default Optional<AppointmentTask> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, appointmentTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    static UpdateDSL<UpdateModel> updateAllColumns(AppointmentTask row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(taskCategory).equalTo(row::getTaskCategory)
                .set(instruction).equalTo(row::getInstruction)
                .set(appointmentId).equalTo(row::getAppointmentId)
                .set(serviceId).equalTo(row::getServiceId)
                .set(careType).equalTo(row::getCareType)
                .set(petId).equalTo(row::getPetId)
                .set(lodgingId).equalTo(row::getLodgingId)
                .set(staffId).equalTo(row::getStaffId)
                .set(startDate).equalTo(row::getStartDate)
                .set(startTime).equalTo(row::getStartTime)
                .set(timeLabel).equalTo(row::getTimeLabel)
                .set(duration).equalTo(row::getDuration)
                .set(status).equalTo(row::getStatus)
                .set(noteStatus).equalTo(row::getNoteStatus)
                .set(noteContent).equalTo(row::getNoteContent)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(addOnId).equalTo(row::getAddOnId)
                .set(noteFeedback).equalTo(row::getNoteFeedback);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AppointmentTask row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(taskCategory).equalToWhenPresent(row::getTaskCategory)
                .set(instruction).equalToWhenPresent(row::getInstruction)
                .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
                .set(serviceId).equalToWhenPresent(row::getServiceId)
                .set(careType).equalToWhenPresent(row::getCareType)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(lodgingId).equalToWhenPresent(row::getLodgingId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(startDate).equalToWhenPresent(row::getStartDate)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(timeLabel).equalToWhenPresent(row::getTimeLabel)
                .set(duration).equalToWhenPresent(row::getDuration)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(noteStatus).equalToWhenPresent(row::getNoteStatus)
                .set(noteContent).equalToWhenPresent(row::getNoteContent)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(addOnId).equalToWhenPresent(row::getAddOnId)
                .set(noteFeedback).equalToWhenPresent(row::getNoteFeedback);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    default int updateByPrimaryKeySelective(AppointmentTask row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(taskCategory).equalToWhenPresent(row::getTaskCategory)
            .set(instruction).equalToWhenPresent(row::getInstruction)
            .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .set(careType).equalToWhenPresent(row::getCareType)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(lodgingId).equalToWhenPresent(row::getLodgingId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(timeLabel).equalToWhenPresent(row::getTimeLabel)
            .set(duration).equalToWhenPresent(row::getDuration)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(noteStatus).equalToWhenPresent(row::getNoteStatus)
            .set(noteContent).equalToWhenPresent(row::getNoteContent)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(addOnId).equalToWhenPresent(row::getAddOnId)
            .set(noteFeedback).equalToWhenPresent(row::getNoteFeedback)
            .where(id, isEqualTo(row::getId))
        );
    }
}