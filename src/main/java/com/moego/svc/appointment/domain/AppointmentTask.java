package com.moego.svc.appointment.domain;

import jakarta.annotation.Generated;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Database Table Remarks:
 *   appointment task management
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table appointment_task
 */
public class AppointmentTask {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.company_id")
    private Long companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.business_id")
    private Long businessId;

    /**
     * Database Column Remarks:
     *   feeding/medication/add_on/pee/poo/custom...
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.task_category")
    private String taskCategory;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.instruction")
    private String instruction;

    /**
     * Database Column Remarks:
     *   optional, moe_grooming_appointment.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   optional, main service, moe_grooming_service.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.service_id")
    private Long serviceId;

    /**
     * Database Column Remarks:
     *   optional, related service item type, 1-grooming, 2-boarding, 3-daycare, 4-evaluation
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.care_type")
    private Integer careType;

    /**
     * Database Column Remarks:
     *   optional, moe_customer_pet.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.pet_id")
    private Long petId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.lodging_id")
    private Long lodgingId;

    /**
     * Database Column Remarks:
     *   optional, moe_staff.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.staff_id")
    private Long staffId;

    /**
     * Database Column Remarks:
     *   feeding/medication date or add-on date
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.start_date")
    private LocalDate startDate;

    /**
     * Database Column Remarks:
     *   feeding/medication time, field is null when it is add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.start_time")
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   feeding/medication time label, example 09:00 is AM
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.time_label")
    private String timeLabel;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.duration")
    private Integer duration;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.status")
    private String status;

    /**
     * Database Column Remarks:
     *   record status information on the completion of various tasks
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_status")
    private String noteStatus;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_content")
    private String noteContent;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.deleted_at")
    private LocalDateTime deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.add_on_id")
    private Long addOnId;

    /**
     * Database Column Remarks:
     *   feeding/medication note feedback
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_feedback")
    private String noteFeedback;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.business_id")
    public Long getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.business_id")
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.task_category")
    public String getTaskCategory() {
        return taskCategory;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.task_category")
    public void setTaskCategory(String taskCategory) {
        this.taskCategory = taskCategory;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.instruction")
    public String getInstruction() {
        return instruction;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.instruction")
    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.service_id")
    public Long getServiceId() {
        return serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.service_id")
    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.care_type")
    public Integer getCareType() {
        return careType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.care_type")
    public void setCareType(Integer careType) {
        this.careType = careType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.lodging_id")
    public Long getLodgingId() {
        return lodgingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.lodging_id")
    public void setLodgingId(Long lodgingId) {
        this.lodgingId = lodgingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.staff_id")
    public Long getStaffId() {
        return staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.staff_id")
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.start_date")
    public LocalDate getStartDate() {
        return startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.start_date")
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.time_label")
    public String getTimeLabel() {
        return timeLabel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.time_label")
    public void setTimeLabel(String timeLabel) {
        this.timeLabel = timeLabel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.duration")
    public Integer getDuration() {
        return duration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.duration")
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.status")
    public String getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.status")
    public void setStatus(String status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_status")
    public String getNoteStatus() {
        return noteStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_status")
    public void setNoteStatus(String noteStatus) {
        this.noteStatus = noteStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_content")
    public String getNoteContent() {
        return noteContent;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_content")
    public void setNoteContent(String noteContent) {
        this.noteContent = noteContent;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.deleted_at")
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.deleted_at")
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.add_on_id")
    public Long getAddOnId() {
        return addOnId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.add_on_id")
    public void setAddOnId(Long addOnId) {
        this.addOnId = addOnId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_feedback")
    public String getNoteFeedback() {
        return noteFeedback;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_task.note_feedback")
    public void setNoteFeedback(String noteFeedback) {
        this.noteFeedback = noteFeedback;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyId=").append(companyId);
        sb.append(", businessId=").append(businessId);
        sb.append(", taskCategory=").append(taskCategory);
        sb.append(", instruction=").append(instruction);
        sb.append(", appointmentId=").append(appointmentId);
        sb.append(", serviceId=").append(serviceId);
        sb.append(", careType=").append(careType);
        sb.append(", petId=").append(petId);
        sb.append(", lodgingId=").append(lodgingId);
        sb.append(", staffId=").append(staffId);
        sb.append(", startDate=").append(startDate);
        sb.append(", startTime=").append(startTime);
        sb.append(", timeLabel=").append(timeLabel);
        sb.append(", duration=").append(duration);
        sb.append(", status=").append(status);
        sb.append(", noteStatus=").append(noteStatus);
        sb.append(", noteContent=").append(noteContent);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", addOnId=").append(addOnId);
        sb.append(", noteFeedback=").append(noteFeedback);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AppointmentTask other = (AppointmentTask) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getTaskCategory() == null ? other.getTaskCategory() == null : this.getTaskCategory().equals(other.getTaskCategory()))
            && (this.getInstruction() == null ? other.getInstruction() == null : this.getInstruction().equals(other.getInstruction()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getServiceId() == null ? other.getServiceId() == null : this.getServiceId().equals(other.getServiceId()))
            && (this.getCareType() == null ? other.getCareType() == null : this.getCareType().equals(other.getCareType()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getLodgingId() == null ? other.getLodgingId() == null : this.getLodgingId().equals(other.getLodgingId()))
            && (this.getStaffId() == null ? other.getStaffId() == null : this.getStaffId().equals(other.getStaffId()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getTimeLabel() == null ? other.getTimeLabel() == null : this.getTimeLabel().equals(other.getTimeLabel()))
            && (this.getDuration() == null ? other.getDuration() == null : this.getDuration().equals(other.getDuration()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getNoteStatus() == null ? other.getNoteStatus() == null : this.getNoteStatus().equals(other.getNoteStatus()))
            && (this.getNoteContent() == null ? other.getNoteContent() == null : this.getNoteContent().equals(other.getNoteContent()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getAddOnId() == null ? other.getAddOnId() == null : this.getAddOnId().equals(other.getAddOnId()))
            && (this.getNoteFeedback() == null ? other.getNoteFeedback() == null : this.getNoteFeedback().equals(other.getNoteFeedback()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_task")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getTaskCategory() == null) ? 0 : getTaskCategory().hashCode());
        result = prime * result + ((getInstruction() == null) ? 0 : getInstruction().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getServiceId() == null) ? 0 : getServiceId().hashCode());
        result = prime * result + ((getCareType() == null) ? 0 : getCareType().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getLodgingId() == null) ? 0 : getLodgingId().hashCode());
        result = prime * result + ((getStaffId() == null) ? 0 : getStaffId().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getTimeLabel() == null) ? 0 : getTimeLabel().hashCode());
        result = prime * result + ((getDuration() == null) ? 0 : getDuration().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getNoteStatus() == null) ? 0 : getNoteStatus().hashCode());
        result = prime * result + ((getNoteContent() == null) ? 0 : getNoteContent().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getAddOnId() == null) ? 0 : getAddOnId().hashCode());
        result = prime * result + ((getNoteFeedback() == null) ? 0 : getNoteFeedback().hashCode());
        return result;
    }
}