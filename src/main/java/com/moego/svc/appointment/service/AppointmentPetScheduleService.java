package com.moego.svc.appointment.service;

import com.moego.idl.models.appointment.v1.PetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.PetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.PetScheduleDef;
import com.moego.lib.common.util.Tx;
import com.moego.svc.appointment.converter.PetFeedingConverter;
import com.moego.svc.appointment.converter.PetMedicationConverter;
import com.moego.svc.appointment.dto.PetDetailDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2024/2/22
 */
@Service
@RequiredArgsConstructor
public class AppointmentPetScheduleService {

    private final AppointmentPetFeedingService feedingService;
    private final AppointmentPetMedicationService medicationService;

    private final PetFeedingConverter feedingConverter;
    private final PetMedicationConverter medicationConverter;

    /**
     * 1. Delete all pet feeding and medication schedules by appointment id and pet ids
     * 2. Insert new pet feeding and medication schedules
     *
     * @param companyId     company id
     * @param appointmentId appointment id
     * @param petSchedules  pet feeding & medication schedules
     */
    @Transactional
    public void reschedulePetFeedingMedication(Long companyId, Long appointmentId, List<PetScheduleDef> petSchedules) {
        List<Long> petIds = petSchedules.stream().map(PetScheduleDef::getPetId).toList();
        feedingService.deleteByPetIds(companyId, appointmentId, petIds);
        medicationService.deleteByPetIds(companyId, appointmentId, petIds);

        List<PetDetailDTO.PetFeedingScheduleDTO> feedings = new ArrayList<>();
        List<PetDetailDTO.PetMedicationScheduleDTO> medications = new ArrayList<>();
        petSchedules.forEach(def -> {
            List<PetDetailDTO.PetFeedingScheduleDTO> feedingSchedules =
                    feedingConverter.defToDTO(def.getFeedingsList());
            feedingSchedules.forEach(dto -> {
                dto.getFeeding().setCompanyId(companyId);
                dto.getFeeding().setAppointmentId(appointmentId);
                dto.getFeeding().setPetId(def.getPetId());
                dto.getScheduleSettings().forEach(scheduleSetting -> {
                    scheduleSetting.setCompanyId(companyId);
                    scheduleSetting.setAppointmentId(appointmentId);
                });
            });
            feedings.addAll(feedingSchedules);

            List<PetDetailDTO.PetMedicationScheduleDTO> medicationSchedules =
                    medicationConverter.defToDTO(def.getMedicationsList());
            medicationSchedules.forEach(dto -> {
                dto.getMedication().setCompanyId(companyId);
                dto.getMedication().setAppointmentId(appointmentId);
                dto.getMedication().setPetId(def.getPetId());
                dto.getScheduleSettings().forEach(scheduleSetting -> {
                    scheduleSetting.setCompanyId(companyId);
                    scheduleSetting.setAppointmentId(appointmentId);
                });
            });
            medications.addAll(medicationSchedules);
        });

        feedingService.insertMultiple(companyId, feedings);
        medicationService.insertMultiple(companyId, medications);
    }

    public void reschedulePetFeeding(Long companyId, Long appointmentId, List<PetFeedingScheduleDef> feedingSchedules) {
        List<Long> petIds =
                feedingSchedules.stream().map(PetFeedingScheduleDef::getPetId).toList();
        Tx.doInTransaction(() -> {
            feedingService.deleteByPetIds(companyId, appointmentId, petIds);

            List<PetDetailDTO.PetFeedingScheduleDTO> feedings = new ArrayList<>();
            feedingSchedules.forEach(def -> {
                List<PetDetailDTO.PetFeedingScheduleDTO> feedingSchedulesDto =
                        feedingConverter.defToDTO(def.getFeedingsList());
                feedingSchedulesDto.forEach(dto -> {
                    dto.getFeeding().setCompanyId(companyId);
                    dto.getFeeding().setAppointmentId(appointmentId);
                    dto.getFeeding().setPetId(def.getPetId());
                    dto.getScheduleSettings().forEach(scheduleSetting -> {
                        scheduleSetting.setCompanyId(companyId);
                        scheduleSetting.setAppointmentId(appointmentId);
                    });
                });
                feedings.addAll(feedingSchedulesDto);
            });

            feedingService.insertMultiple(companyId, feedings);
        });
    }

    public void reschedulePetMedication(
            Long companyId, Long appointmentId, List<PetMedicationScheduleDef> medicationSchedules) {
        List<Long> petIds = medicationSchedules.stream()
                .map(PetMedicationScheduleDef::getPetId)
                .toList();
        Tx.doInTransaction(() -> {
            medicationService.deleteByPetIds(companyId, appointmentId, petIds);
            List<PetDetailDTO.PetMedicationScheduleDTO> medications = new ArrayList<>();
            medicationSchedules.forEach(def -> {
                List<PetDetailDTO.PetMedicationScheduleDTO> medicationSchedulesDto =
                        medicationConverter.defToDTO(def.getMedicationsList());
                medicationSchedulesDto.forEach(dto -> {
                    dto.getMedication().setCompanyId(companyId);
                    dto.getMedication().setAppointmentId(appointmentId);
                    dto.getMedication().setPetId(def.getPetId());
                    dto.getScheduleSettings().forEach(scheduleSetting -> {
                        scheduleSetting.setCompanyId(companyId);
                        scheduleSetting.setAppointmentId(appointmentId);
                    });
                });
                medications.addAll(medicationSchedulesDto);
            });

            medicationService.insertMultiple(companyId, medications);
        });
    }
}
