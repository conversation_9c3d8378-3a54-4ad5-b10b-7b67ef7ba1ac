package com.moego.svc.appointment.converter;

import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentTaskCategory;
import com.moego.idl.models.appointment.v1.AppointmentTaskModel;
import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.CountAppointmentTasksRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskGroupsRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentTaskRequest;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.appointment.domain.AppointmentPetFeeding;
import com.moego.svc.appointment.domain.AppointmentPetMedication;
import com.moego.svc.appointment.domain.AppointmentPetScheduleSetting;
import com.moego.svc.appointment.domain.AppointmentTask;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/1/16
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        uses = {TimeConverter.class})
public interface AppointmentTaskConverter {

    AppointmentTaskConverter INSTANCE = Mappers.getMapper(AppointmentTaskConverter.class);

    List<AppointmentTaskModel> toModel(List<AppointmentTask> entities);

    AppointmentTaskModel toModel(AppointmentTask entity);

    AppointmentTask toEntity(UpdateAppointmentTaskRequest request);

    default Integer enumToInteger(ServiceItemType careType) {
        return careType != null ? careType.getNumber() : null;
    }

    default ServiceItemType integerToEnum(Integer careType) {
        return careType != null ? ServiceItemType.forNumber(careType) : ServiceItemType.SERVICE_ITEM_TYPE_UNSPECIFIED;
    }

    CountAppointmentTasksRequest.Filter toFilter(ListAppointmentTaskGroupsRequest.Filter filter);

    default List<AppointmentTask> inheritBasicTaskStatus(List<AppointmentTask> tasks, List<AppointmentTask> oldTasks) {
        var keyToBasicTask = oldTasks.stream()
                .filter(this::isBasicTask)
                .collect(Collectors.toMap(this::buildBasicKey, Function.identity(), (o, n) -> o));
        tasks.forEach(task -> {
            var originalTask = keyToBasicTask.get(buildBasicKey(task));
            if (originalTask != null) {
                inheritedOldTask(task, originalTask);
            }
        });
        return tasks;
    }

    default List<AppointmentTask> inheritAddOnTaskStatus(List<AppointmentTask> tasks, List<AppointmentTask> oldTasks) {
        var keyToAddOnTask = oldTasks.stream()
                .filter(task -> Objects.equals(task.getTaskCategory(), AppointmentTaskCategory.ADD_ONS.name()))
                .collect(Collectors.toMap(this::buildAddOnKey, Function.identity(), (o, n) -> o));
        tasks.forEach(task -> {
            var originalTask = keyToAddOnTask.get(buildAddOnKey(task));
            if (originalTask != null) {
                inheritedOldTask(task, originalTask);
            }
        });
        return tasks;
    }

    default boolean isBasicTask(AppointmentTask task) {
        return Objects.equals(task.getTaskCategory(), AppointmentTaskCategory.FEEDING.name())
                || Objects.equals(task.getTaskCategory(), AppointmentTaskCategory.MEDICATION.name());
    }

    default String buildBasicKey(AppointmentTask task) {
        return String.format(
                "%s-%s-%s-%s-%s-%s-%s",
                task.getAppointmentId(),
                task.getPetId(),
                task.getServiceId(),
                task.getTaskCategory(),
                task.getStartDate(),
                task.getStartTime(),
                task.getTimeLabel());
    }

    default String buildAddOnKey(AppointmentTask task) {
        return String.format(
                "%s-%s-%s-%s-%s-%s-%s",
                task.getAppointmentId(),
                task.getPetId(),
                task.getServiceId(),
                task.getTaskCategory(),
                task.getStartDate(),
                task.getAddOnId(),
                task.getInstruction());
    }

    default void inheritedOldTask(AppointmentTask task, AppointmentTask originalTask) {
        task.setNoteStatus(originalTask.getNoteStatus());
        task.setNoteContent(originalTask.getNoteContent());
        task.setNoteFeedback(originalTask.getNoteFeedback());
        if (!Objects.equals(originalTask.getStatus(), AppointmentTaskStatus.INCOMPLETE.name())) {
            task.setStatus(originalTask.getStatus());
        }
        task.setStaffId(originalTask.getStaffId());
    }

    default String buildTaskInstruction(AppointmentPetFeeding feeding) {
        StringJoiner mainJoiner = new StringJoiner(", ");

        // amount unit
        if (StringUtils.hasText(feeding.getFeedingAmount()) && StringUtils.hasText(feeding.getFeedingUnit())) {
            mainJoiner.add(feeding.getFeedingAmount() + " " + feeding.getFeedingUnit());
        }

        // type, source, instruction
        Stream.of(feeding.getFeedingType(), feeding.getFeedingSource(), feeding.getFeedingInstruction())
                .filter(StringUtils::hasText)
                .forEach(mainJoiner::add);

        // note
        String result = mainJoiner.toString();
        if (StringUtils.hasText(feeding.getFeedingNote())) {
            if (!result.isEmpty()) {
                result += "; ";
            }
            result += feeding.getFeedingNote();
        }

        // "%s %s, %s, %s, %s; %s"
        return result;
    }

    default String buildTaskInstruction(AppointmentPetMedication medication) {
        StringJoiner mainJoiner = new StringJoiner(", ");

        // amount unit
        if (StringUtils.hasText(medication.getMedicationAmount())
                && StringUtils.hasText(medication.getMedicationUnit())) {
            mainJoiner.add(medication.getMedicationAmount() + " " + medication.getMedicationUnit());
        }

        // medication name
        if (StringUtils.hasText(medication.getMedicationName())) {
            mainJoiner.add(medication.getMedicationName());
        }

        // note
        String result = mainJoiner.toString();
        if (StringUtils.hasText(medication.getMedicationNote())) {
            if (!result.isEmpty()) {
                result += "; " + medication.getMedicationNote();
            } else {
                result = medication.getMedicationNote();
            }
        }

        // "%s %s, %s; %s"
        return result;
    }

    default String buildTaskInstruction(String serviceName, int sequence, int total) {
        if (total == 1) {
            return serviceName;
        }
        return String.format("%s (%d/%d)", serviceName, sequence, total);
    }

    default String getTimeLabel(AppointmentPetScheduleSetting time) {
        var scheduleExtraJson = time.getScheduleExtraJson();
        var map = JsonUtil.toBean(scheduleExtraJson, Map.class);
        return (String) map.get("label");
    }

    default AppointmentTaskStatus buildTaskStatus(byte appointmentStatus) {
        return Objects.equals((int) appointmentStatus, AppointmentStatus.FINISHED_VALUE)
                ? AppointmentTaskStatus.COMPLETED
                : AppointmentTaskStatus.INCOMPLETE;
    }
}
