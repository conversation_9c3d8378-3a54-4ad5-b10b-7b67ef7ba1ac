package com.moego.svc.appointment.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.service.appointment.v1.PatchAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.PatchAppointmentTaskResponse;
import com.moego.idl.service.appointment.v1.UpdateAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentTaskResponse;
import com.moego.svc.appointment.domain.AppointmentTask;
import com.moego.svc.appointment.service.AppointmentTaskService;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AppointmentTaskControllerTest {

    @Mock
    private AppointmentTaskService appointmentTaskService;

    @InjectMocks
    private AppointmentTaskController appointmentTaskController;

    @Test
    void testPatchAppointmentTask_WithStaffIdAndStatus() {
        // 准备测试数据
        long taskId = 1L;
        long staffId = 100L;
        long tokenStaffId = 200L;
        AppointmentTaskStatus status = AppointmentTaskStatus.COMPLETED;
        String noteContent = "Task completed successfully";
        String noteFeedback = "\uD83C\uDF7D\uFE0F Ate all (100%)";

        // 创建原始任务（用于 getAssignedStaff 方法）
        AppointmentTask originalTask = new AppointmentTask();
        originalTask.setId(taskId);
        originalTask.setStaffId(50L); // 原始 staff ID

        // 模拟 appointmentTaskService.mustGet 方法
        when(appointmentTaskService.mustGet(taskId)).thenReturn(originalTask);

        // 模拟 appointmentTaskService.updateSelective 方法返回成功更新
        when(appointmentTaskService.updateSelective(any(AppointmentTask.class))).thenReturn(1);

        // 创建请求
        PatchAppointmentTaskRequest request = PatchAppointmentTaskRequest.newBuilder()
                .setTaskId(taskId)
                .setStaffId(staffId)
                .setStatus(status)
                .setNoteContent(noteContent)
                .setNoteFeedback(noteFeedback)
                .setTokenStaffId(tokenStaffId)
                .build();

        // 创建响应观察者
        StreamObserver<PatchAppointmentTaskResponse> responseObserver = mock(StreamObserver.class);

        // 执行方法
        appointmentTaskController.patchAppointmentTask(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<PatchAppointmentTaskResponse> responseCaptor =
                ArgumentCaptor.forClass(PatchAppointmentTaskResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应
        PatchAppointmentTaskResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertTrue(response.getUpdated());

        // 验证服务调用
        verify(appointmentTaskService).mustGet(taskId);

        // 捕获传递给 updateSelective 的任务对象
        ArgumentCaptor<AppointmentTask> taskCaptor = ArgumentCaptor.forClass(AppointmentTask.class);
        verify(appointmentTaskService).updateSelective(taskCaptor.capture());

        AppointmentTask capturedTask = taskCaptor.getValue();
        assertEquals(taskId, capturedTask.getId());
        assertEquals(staffId, capturedTask.getStaffId());
        assertEquals(status.name(), capturedTask.getStatus());
        assertEquals(noteContent, capturedTask.getNoteContent());
        assertEquals(noteFeedback, capturedTask.getNoteFeedback());
    }

    @Test
    void testPatchAppointmentTask_AutoAssignStaffOnComplete() {
        // 准备测试数据 - 测试完成任务时自动分配当前 token staff
        long taskId = 2L;
        long tokenStaffId = 300L;
        AppointmentTaskStatus status = AppointmentTaskStatus.COMPLETED;

        // 创建原始任务（没有分配 staff）
        AppointmentTask originalTask = new AppointmentTask();
        originalTask.setId(taskId);
        originalTask.setStaffId(null); // 没有分配 staff

        // 模拟 appointmentTaskService.mustGet 方法
        when(appointmentTaskService.mustGet(taskId)).thenReturn(originalTask);

        // 模拟 appointmentTaskService.updateSelective 方法返回成功更新
        when(appointmentTaskService.updateSelective(any(AppointmentTask.class))).thenReturn(1);

        // 创建请求（不包含 staffId，但包含 tokenStaffId 和 COMPLETED 状态）
        PatchAppointmentTaskRequest request = PatchAppointmentTaskRequest.newBuilder()
                .setTaskId(taskId)
                .setStatus(status)
                .setTokenStaffId(tokenStaffId)
                .build();

        // 创建响应观察者
        StreamObserver<PatchAppointmentTaskResponse> responseObserver = mock(StreamObserver.class);

        // 执行方法
        appointmentTaskController.patchAppointmentTask(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<PatchAppointmentTaskResponse> responseCaptor =
                ArgumentCaptor.forClass(PatchAppointmentTaskResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应
        PatchAppointmentTaskResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertTrue(response.getUpdated());

        // 验证服务调用
        verify(appointmentTaskService).mustGet(taskId);

        // 捕获传递给 updateSelective 的任务对象
        ArgumentCaptor<AppointmentTask> taskCaptor = ArgumentCaptor.forClass(AppointmentTask.class);
        verify(appointmentTaskService).updateSelective(taskCaptor.capture());

        AppointmentTask capturedTask = taskCaptor.getValue();
        assertEquals(taskId, capturedTask.getId());
        assertEquals(tokenStaffId, capturedTask.getStaffId()); // 应该自动分配 tokenStaffId
        assertEquals(status.name(), capturedTask.getStatus());
    }

    @Test
    void testPatchAppointmentTask_UpdateFailed() {
        // 准备测试数据 - 测试更新失败的情况
        long taskId = 3L;
        long staffId = 400L;
        AppointmentTaskStatus status = AppointmentTaskStatus.INCOMPLETE;

        // 创建原始任务
        AppointmentTask originalTask = new AppointmentTask();
        originalTask.setId(taskId);
        originalTask.setStaffId(100L);

        // 模拟 appointmentTaskService.mustGet 方法
        when(appointmentTaskService.mustGet(taskId)).thenReturn(originalTask);

        // 模拟 appointmentTaskService.updateSelective 方法返回更新失败（0 行受影响）
        when(appointmentTaskService.updateSelective(any(AppointmentTask.class))).thenReturn(0);

        // 创建请求
        PatchAppointmentTaskRequest request = PatchAppointmentTaskRequest.newBuilder()
                .setTaskId(taskId)
                .setStaffId(staffId)
                .setStatus(status)
                .build();

        // 创建响应观察者
        StreamObserver<PatchAppointmentTaskResponse> responseObserver = mock(StreamObserver.class);

        // 执行方法
        appointmentTaskController.patchAppointmentTask(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<PatchAppointmentTaskResponse> responseCaptor =
                ArgumentCaptor.forClass(PatchAppointmentTaskResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应
        PatchAppointmentTaskResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertFalse(response.getUpdated()); // 更新失败

        // 验证服务调用
        verify(appointmentTaskService).mustGet(taskId);
        verify(appointmentTaskService).updateSelective(any(AppointmentTask.class));
    }

    @Test
    void testUpdateAppointmentTask_Success() {
        // 准备测试数据
        long taskId = 4L;
        long appointmentId = 1000L;
        long serviceId = 2000L;
        long petId = 3000L;
        long staffId = 4000L;
        String instruction = "Feed the pet at 9 AM";
        String noteContent = "Pet ate well";

        // 创建请求
        UpdateAppointmentTaskRequest request = UpdateAppointmentTaskRequest.newBuilder()
                .setId(taskId)
                .setAppointmentId(appointmentId)
                .setServiceId(serviceId)
                .setPetId(petId)
                .setStaffId(staffId)
                .setInstruction(instruction)
                .setNoteContent(noteContent)
                .setStatus(AppointmentTaskStatus.COMPLETED)
                .build();

        // 模拟 AppointmentTaskConverter.INSTANCE.toEntity 方法
        AppointmentTask convertedTask = new AppointmentTask();
        convertedTask.setId(taskId);
        convertedTask.setAppointmentId(appointmentId);
        convertedTask.setServiceId(serviceId);
        convertedTask.setPetId(petId);
        convertedTask.setStaffId(staffId);
        convertedTask.setInstruction(instruction);
        convertedTask.setNoteContent(noteContent);
        convertedTask.setStatus(AppointmentTaskStatus.COMPLETED.name());

        // 模拟 appointmentTaskService.updateSelective 方法返回成功更新
        when(appointmentTaskService.updateSelective(any(AppointmentTask.class))).thenReturn(1);

        // 创建响应观察者
        StreamObserver<UpdateAppointmentTaskResponse> responseObserver = mock(StreamObserver.class);

        // 执行方法
        appointmentTaskController.updateAppointmentTask(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<UpdateAppointmentTaskResponse> responseCaptor =
                ArgumentCaptor.forClass(UpdateAppointmentTaskResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应
        UpdateAppointmentTaskResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertTrue(response.getUpdated());

        // 验证服务调用
        verify(appointmentTaskService).updateSelective(any(AppointmentTask.class));
    }

    @Test
    void testUpdateAppointmentTask_UpdateFailed() {
        // 准备测试数据 - 测试更新失败的情况
        long taskId = 5L;
        long appointmentId = 1001L;
        long petId = 3001L;
        String instruction = "Give medication";

        // 创建请求
        UpdateAppointmentTaskRequest request = UpdateAppointmentTaskRequest.newBuilder()
                .setId(taskId)
                .setAppointmentId(appointmentId)
                .setPetId(petId)
                .setInstruction(instruction)
                .setStatus(AppointmentTaskStatus.INCOMPLETE)
                .build();

        // 模拟 appointmentTaskService.updateSelective 方法返回更新失败（0 行受影响）
        when(appointmentTaskService.updateSelective(any(AppointmentTask.class))).thenReturn(0);

        // 创建响应观察者
        StreamObserver<UpdateAppointmentTaskResponse> responseObserver = mock(StreamObserver.class);

        // 执行方法
        appointmentTaskController.updateAppointmentTask(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<UpdateAppointmentTaskResponse> responseCaptor =
                ArgumentCaptor.forClass(UpdateAppointmentTaskResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应
        UpdateAppointmentTaskResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertFalse(response.getUpdated()); // 更新失败

        // 验证服务调用
        verify(appointmentTaskService).updateSelective(any(AppointmentTask.class));
    }

    @Test
    void testUpdateAppointmentTask_MultipleRowsAffected() {
        // 准备测试数据 - 测试多行受影响的情况（不应该发生，但测试边界情况）
        long taskId = 6L;
        long appointmentId = 1002L;

        // 创建请求
        UpdateAppointmentTaskRequest request = UpdateAppointmentTaskRequest.newBuilder()
                .setId(taskId)
                .setAppointmentId(appointmentId)
                .setStatus(AppointmentTaskStatus.COMPLETED)
                .build();

        // 模拟 appointmentTaskService.updateSelective 方法返回多行受影响
        when(appointmentTaskService.updateSelective(any(AppointmentTask.class))).thenReturn(2);

        // 创建响应观察者
        StreamObserver<UpdateAppointmentTaskResponse> responseObserver = mock(StreamObserver.class);

        // 执行方法
        appointmentTaskController.updateAppointmentTask(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<UpdateAppointmentTaskResponse> responseCaptor =
                ArgumentCaptor.forClass(UpdateAppointmentTaskResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应
        UpdateAppointmentTaskResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertFalse(response.getUpdated()); // 应该返回 false，因为不等于 1

        // 验证服务调用
        verify(appointmentTaskService).updateSelective(any(AppointmentTask.class));
    }
}
