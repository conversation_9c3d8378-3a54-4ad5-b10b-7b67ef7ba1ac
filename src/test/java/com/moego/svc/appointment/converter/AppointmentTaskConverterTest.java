package com.moego.svc.appointment.converter;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.type.Date;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentTaskCategory;
import com.moego.idl.models.appointment.v1.AppointmentTaskModel;
import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.UpdateAppointmentTaskRequest;
import com.moego.svc.appointment.domain.AppointmentPetFeeding;
import com.moego.svc.appointment.domain.AppointmentPetMedication;
import com.moego.svc.appointment.domain.AppointmentTask;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;

class AppointmentTaskConverterTest {

    @Test
    void should_build_feeding_instruction_correctly() {
        // Arrange
        var feeding = new AppointmentPetFeeding();
        feeding.setFeedingAmount("100");
        feeding.setFeedingUnit("g");
        feeding.setFeedingType("Dry Food");
        feeding.setFeedingSource("Royal Canin");
        feeding.setFeedingInstruction("Mix with water");
        feeding.setFeedingNote("Feed slowly");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(feeding);

        // Assert
        assertThat(instruction).isEqualTo("100 g, Dry Food, Royal Canin, Mix with water; Feed slowly");
    }

    @Test
    void should_build_feeding_instruction_correctly_when_amount_and_unit_missing() {
        // Arrange
        var feeding = new AppointmentPetFeeding();
        feeding.setFeedingAmount("");
        feeding.setFeedingUnit(null);
        feeding.setFeedingType("Dry Food");
        feeding.setFeedingSource("Royal Canin");
        feeding.setFeedingInstruction("Mix with water");
        feeding.setFeedingNote("Feed slowly");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(feeding);

        // Assert
        assertThat(instruction).isEqualTo("Dry Food, Royal Canin, Mix with water; Feed slowly");
    }

    @Test
    void should_build_feeding_instruction_correctly_when_only_amount_present() {
        // Arrange
        var feeding = new AppointmentPetFeeding();
        feeding.setFeedingAmount("100");
        feeding.setFeedingUnit("");
        feeding.setFeedingType("Dry Food");
        feeding.setFeedingSource("Royal Canin");
        feeding.setFeedingInstruction("Mix with water");
        feeding.setFeedingNote("Feed slowly");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(feeding);

        // Assert
        assertThat(instruction).isEqualTo("Dry Food, Royal Canin, Mix with water; Feed slowly");
    }

    @Test
    void should_build_feeding_instruction_correctly_when_note_missing() {
        // Arrange
        var feeding = new AppointmentPetFeeding();
        feeding.setFeedingAmount("100");
        feeding.setFeedingUnit("g");
        feeding.setFeedingType("Dry Food");
        feeding.setFeedingSource("Royal Canin");
        feeding.setFeedingInstruction("Mix with water");
        feeding.setFeedingNote("");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(feeding);

        // Assert
        assertThat(instruction).isEqualTo("100 g, Dry Food, Royal Canin, Mix with water");
    }

    @Test
    void should_build_feeding_instruction_correctly_when_middle_fields_missing() {
        // Arrange
        var feeding = new AppointmentPetFeeding();
        feeding.setFeedingAmount("100");
        feeding.setFeedingUnit("g");
        feeding.setFeedingType("");
        feeding.setFeedingSource(null);
        feeding.setFeedingInstruction("Mix with water");
        feeding.setFeedingNote("Feed slowly");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(feeding);

        // Assert
        assertThat(instruction).isEqualTo("100 g, Mix with water; Feed slowly");
    }

    @Test
    void should_build_feeding_instruction_correctly_when_all_fields_empty() {
        // Arrange
        var feeding = new AppointmentPetFeeding();
        feeding.setFeedingAmount("");
        feeding.setFeedingUnit("");
        feeding.setFeedingType("");
        feeding.setFeedingSource("");
        feeding.setFeedingInstruction("");
        feeding.setFeedingNote("");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(feeding);

        // Assert
        assertThat(instruction).isEqualTo("");
    }

    @Test
    void should_build_feeding_instruction_correctly_when_only_note_present() {
        // Arrange
        var feeding = new AppointmentPetFeeding();
        feeding.setFeedingAmount("");
        feeding.setFeedingUnit("");
        feeding.setFeedingType("");
        feeding.setFeedingSource("");
        feeding.setFeedingInstruction("");
        feeding.setFeedingNote("Feed slowly");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(feeding);

        // Assert
        assertThat(instruction).isEqualTo("Feed slowly");
    }

    @Test
    void should_build_medication_instruction_correctly() {
        // Arrange
        var medication = new AppointmentPetMedication();
        medication.setMedicationAmount("2");
        medication.setMedicationUnit("pills");
        medication.setMedicationName("Vitamin C");
        medication.setMedicationNote("After meal");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(medication);

        // Assert
        assertThat(instruction).isEqualTo("2 pills, Vitamin C; After meal");
    }

    @Test
    void should_build_medication_instruction_correctly_when_amount_and_unit_missing() {
        // Arrange
        var medication = new AppointmentPetMedication();
        medication.setMedicationAmount("");
        medication.setMedicationUnit(null);
        medication.setMedicationName("Antibiotics");
        medication.setMedicationNote("After meals");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(medication);

        // Assert
        assertThat(instruction).isEqualTo("Antibiotics; After meals");
    }

    @Test
    void should_build_medication_instruction_correctly_when_only_amount_present() {
        // Arrange
        var medication = new AppointmentPetMedication();
        medication.setMedicationAmount("10");
        medication.setMedicationUnit("");
        medication.setMedicationName("Antibiotics");
        medication.setMedicationNote("After meals");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(medication);

        // Assert
        assertThat(instruction).isEqualTo("Antibiotics; After meals");
    }

    @Test
    void should_build_medication_instruction_correctly_when_note_missing() {
        // Arrange
        var medication = new AppointmentPetMedication();
        medication.setMedicationAmount("10");
        medication.setMedicationUnit("mg");
        medication.setMedicationName("Antibiotics");
        medication.setMedicationNote("");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(medication);

        // Assert
        assertThat(instruction).isEqualTo("10 mg, Antibiotics");
    }

    @Test
    void should_build_medication_instruction_correctly_when_name_missing() {
        // Arrange
        var medication = new AppointmentPetMedication();
        medication.setMedicationAmount("10");
        medication.setMedicationUnit("mg");
        medication.setMedicationName("");
        medication.setMedicationNote("After meals");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(medication);

        // Assert
        assertThat(instruction).isEqualTo("10 mg; After meals");
    }

    @Test
    void should_build_medication_instruction_correctly_when_all_fields_empty() {
        // Arrange
        var medication = new AppointmentPetMedication();
        medication.setMedicationAmount("");
        medication.setMedicationUnit("");
        medication.setMedicationName("");
        medication.setMedicationNote("");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(medication);

        // Assert
        assertThat(instruction).isEqualTo("");
    }

    @Test
    void should_build_medication_instruction_correctly_when_only_note_present() {
        // Arrange
        var medication = new AppointmentPetMedication();
        medication.setMedicationAmount("");
        medication.setMedicationUnit("");
        medication.setMedicationName("");
        medication.setMedicationNote("After meals");

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(medication);

        // Assert
        assertThat(instruction).isEqualTo("After meals");
    }

    @Test
    void should_build_service_instruction_without_sequence_when_total_is_one() {
        // Arrange
        var serviceName = "Grooming";
        var sequence = 1;
        var total = 1;

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(serviceName, sequence, total);

        // Assert
        assertThat(instruction).isEqualTo("Grooming");
    }

    @Test
    void should_build_service_instruction_with_sequence_when_total_greater_than_one() {
        // Arrange
        var serviceName = "Walk";
        var sequence = 2;
        var total = 3;

        // Act
        var instruction = AppointmentTaskConverter.INSTANCE.buildTaskInstruction(serviceName, sequence, total);

        // Assert
        assertThat(instruction).isEqualTo("Walk (2/3)");
    }

    @Test
    void buildTaskStatus_shouldReturnIncomplete() {
        // Arrange
        var appointmentStatus = (byte) AppointmentStatus.UNCONFIRMED_VALUE;

        // Act
        var result = AppointmentTaskConverter.INSTANCE.buildTaskStatus(appointmentStatus);

        // Assert
        assertThat(result).isEqualTo(AppointmentTaskStatus.INCOMPLETE);
    }

    @Test
    void buildTaskStatus_shouldReturnCompleted() {
        // Arrange
        var appointmentStatus = (byte) AppointmentStatus.FINISHED_VALUE;

        // Act
        var result = AppointmentTaskConverter.INSTANCE.buildTaskStatus(appointmentStatus);

        // Assert
        assertThat(result).isEqualTo(AppointmentTaskStatus.COMPLETED);
    }

    @Test
    void testToModel_WithCompleteAppointmentTask() {
        // Arrange
        AppointmentTask entity = new AppointmentTask();
        entity.setId(1L);
        entity.setCompanyId(100L);
        entity.setBusinessId(200L);
        entity.setTaskCategory(AppointmentTaskCategory.FEEDING.name());
        entity.setInstruction("Feed the pet with 100g dry food");
        entity.setAppointmentId(1000L);
        entity.setServiceId(2000L);
        entity.setPetId(3000L);
        entity.setLodgingId(4000L);
        entity.setStaffId(5000L);
        entity.setStartDate(LocalDate.of(2024, 1, 15));
        entity.setStartTime(540); // 9:00 AM
        entity.setTimeLabel("AM");
        entity.setDuration(30);
        entity.setStatus(AppointmentTaskStatus.COMPLETED.name());
        entity.setNoteStatus("GOOD");
        entity.setNoteContent("Pet ate well");
        entity.setNoteFeedback("\uD83C\uDF7D\uFE0F Ate all (100%)");
        entity.setCareType(ServiceItemType.BOARDING_VALUE);
        entity.setAddOnId(6000L);
        entity.setCreatedAt(LocalDateTime.of(2024, 1, 15, 8, 0));
        entity.setUpdatedAt(LocalDateTime.of(2024, 1, 15, 9, 30));

        // Act
        AppointmentTaskModel result = AppointmentTaskConverter.INSTANCE.toModel(entity);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getTaskCategory()).isEqualTo(AppointmentTaskCategory.FEEDING);
        assertThat(result.getInstruction()).isEqualTo("Feed the pet with 100g dry food");
        assertThat(result.getAppointmentId()).isEqualTo(1000L);
        assertThat(result.getServiceId()).isEqualTo(2000L);
        assertThat(result.getPetId()).isEqualTo(3000L);
        assertThat(result.getLodgingId()).isEqualTo(4000L);
        assertThat(result.getStaffId()).isEqualTo(5000L);
        assertThat(result.getStartDate())
                .isEqualTo(
                        Date.newBuilder().setYear(2024).setMonth(1).setDay(15).build());
        assertThat(result.getStartTime()).isEqualTo(540);
        assertThat(result.getTimeLabel()).isEqualTo("AM");
        assertThat(result.getDuration()).isEqualTo(30);
        assertThat(result.getStatus()).isEqualTo(AppointmentTaskStatus.COMPLETED);
        assertThat(result.getNoteContent()).isEqualTo("Pet ate well");
        assertThat(result.getNoteFeedback()).isEqualTo("\uD83C\uDF7D\uFE0F Ate all (100%)");
        assertThat(result.getCareType()).isEqualTo(ServiceItemType.BOARDING);
        assertThat(result.getAddOnId()).isEqualTo(6000L);
    }

    @Test
    void testToModel_WithMinimalAppointmentTask() {
        // Arrange - 测试只有必需字段的情况
        AppointmentTask entity = new AppointmentTask();
        entity.setId(2L);
        entity.setTaskCategory(AppointmentTaskCategory.MEDICATION.name());
        entity.setInstruction("Give medication");
        entity.setStatus(AppointmentTaskStatus.INCOMPLETE.name());

        // Act
        AppointmentTaskModel result = AppointmentTaskConverter.INSTANCE.toModel(entity);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(2L);
        assertThat(result.getTaskCategory()).isEqualTo(AppointmentTaskCategory.MEDICATION);
        assertThat(result.getInstruction()).isEqualTo("Give medication");
        assertThat(result.getStatus()).isEqualTo(AppointmentTaskStatus.INCOMPLETE);

        // 验证可选字段的默认值
        assertThat(result.getAppointmentId()).isEqualTo(0L);
        assertThat(result.getServiceId()).isEqualTo(0L);
        assertThat(result.getPetId()).isEqualTo(0L);
        assertThat(result.getLodgingId()).isEqualTo(0L);
        assertThat(result.getStaffId()).isEqualTo(0L);
        assertThat(result.getAddOnId()).isEqualTo(0L);
        assertThat(result.hasStartTime()).isFalse();
        assertThat(result.hasTimeLabel()).isFalse();
        assertThat(result.hasDuration()).isFalse();
    }

    @Test
    void testToModel_WithNullValues() {
        // Arrange - 测试 null 值的处理
        AppointmentTask entity = new AppointmentTask();
        entity.setId(3L);
        entity.setTaskCategory(AppointmentTaskCategory.ADD_ONS.name());
        entity.setInstruction("Custom task");
        entity.setStatus(AppointmentTaskStatus.INCOMPLETE.name());
        entity.setStartDate(null);
        entity.setStartTime(null);
        entity.setTimeLabel(null);
        entity.setDuration(null);
        entity.setCareType(null);

        // Act
        AppointmentTaskModel result = AppointmentTaskConverter.INSTANCE.toModel(entity);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(3L);
        assertThat(result.getTaskCategory()).isEqualTo(AppointmentTaskCategory.ADD_ONS);
        assertThat(result.getInstruction()).isEqualTo("Custom task");
        assertThat(result.getStatus()).isEqualTo(AppointmentTaskStatus.INCOMPLETE);
        assertThat(result.getCareType()).isEqualTo(ServiceItemType.SERVICE_ITEM_TYPE_UNSPECIFIED);
        assertThat(result.hasStartTime()).isFalse();
        assertThat(result.hasTimeLabel()).isFalse();
        assertThat(result.hasDuration()).isFalse();
    }

    @Test
    void testToModel_WithList() {
        // Arrange
        AppointmentTask entity1 = new AppointmentTask();
        entity1.setId(1L);
        entity1.setTaskCategory(AppointmentTaskCategory.FEEDING.name());
        entity1.setInstruction("Feed pet 1");
        entity1.setStatus(AppointmentTaskStatus.COMPLETED.name());

        AppointmentTask entity2 = new AppointmentTask();
        entity2.setId(2L);
        entity2.setTaskCategory(AppointmentTaskCategory.MEDICATION.name());
        entity2.setInstruction("Give medication to pet 2");
        entity2.setStatus(AppointmentTaskStatus.INCOMPLETE.name());

        List<AppointmentTask> entities = List.of(entity1, entity2);

        // Act
        List<AppointmentTaskModel> results = AppointmentTaskConverter.INSTANCE.toModel(entities);

        // Assert
        assertThat(results).isNotNull();
        assertThat(results).hasSize(2);

        AppointmentTaskModel result1 = results.get(0);
        assertThat(result1.getId()).isEqualTo(1L);
        assertThat(result1.getTaskCategory()).isEqualTo(AppointmentTaskCategory.FEEDING);
        assertThat(result1.getInstruction()).isEqualTo("Feed pet 1");
        assertThat(result1.getStatus()).isEqualTo(AppointmentTaskStatus.COMPLETED);

        AppointmentTaskModel result2 = results.get(1);
        assertThat(result2.getId()).isEqualTo(2L);
        assertThat(result2.getTaskCategory()).isEqualTo(AppointmentTaskCategory.MEDICATION);
        assertThat(result2.getInstruction()).isEqualTo("Give medication to pet 2");
        assertThat(result2.getStatus()).isEqualTo(AppointmentTaskStatus.INCOMPLETE);
    }

    @Test
    void testToModel_WithEmptyList() {
        // Arrange
        List<AppointmentTask> entities = List.of();

        // Act
        List<AppointmentTaskModel> results = AppointmentTaskConverter.INSTANCE.toModel(entities);

        // Assert
        assertThat(results).isNotNull();
        assertThat(results).isEmpty();
    }

    @Test
    void testToEntity_WithCompleteUpdateRequest() {
        // Arrange
        UpdateAppointmentTaskRequest request = UpdateAppointmentTaskRequest.newBuilder()
                .setId(1L)
                .setTaskCategory(AppointmentTaskCategory.FEEDING)
                .setInstruction("Feed the pet with premium food")
                .setAppointmentId(1000L)
                .setServiceId(2000L)
                .setPetId(3000L)
                .setLodgingId(4000L)
                .setStaffId(5000L)
                .setStartDate(
                        Date.newBuilder().setYear(2024).setMonth(2).setDay(20).build())
                .setStartTime(720) // 12:00 PM
                .setTimeLabel("PM")
                .setDuration(45)
                .setStatus(AppointmentTaskStatus.INCOMPLETE)
                .setNoteContent("Pet is eating well")
                .setCareType(ServiceItemType.DAYCARE)
                .setNoteFeedback("Good progress")
                .build();

        // Act
        AppointmentTask result = AppointmentTaskConverter.INSTANCE.toEntity(request);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getTaskCategory()).isEqualTo(AppointmentTaskCategory.FEEDING.name());
        assertThat(result.getInstruction()).isEqualTo("Feed the pet with premium food");
        assertThat(result.getAppointmentId()).isEqualTo(1000L);
        assertThat(result.getServiceId()).isEqualTo(2000L);
        assertThat(result.getPetId()).isEqualTo(3000L);
        assertThat(result.getLodgingId()).isEqualTo(4000L);
        assertThat(result.getStaffId()).isEqualTo(5000L);
        assertThat(result.getStartDate()).isEqualTo(LocalDate.of(2024, 2, 20));
        assertThat(result.getStartTime()).isEqualTo(720);
        assertThat(result.getTimeLabel()).isEqualTo("PM");
        assertThat(result.getDuration()).isEqualTo(45);
        assertThat(result.getStatus()).isEqualTo(AppointmentTaskStatus.INCOMPLETE.name());
        assertThat(result.getNoteContent()).isEqualTo("Pet is eating well");
        assertThat(result.getCareType()).isEqualTo(ServiceItemType.DAYCARE_VALUE);
        assertThat(result.getNoteFeedback()).isEqualTo("Good progress");
    }

    @Test
    void testToEntity_WithMinimalUpdateRequest() {
        // Arrange - 测试只有必需字段的情况
        UpdateAppointmentTaskRequest request = UpdateAppointmentTaskRequest.newBuilder()
                .setId(2L)
                .setTaskCategory(AppointmentTaskCategory.MEDICATION)
                .setInstruction("Give medication")
                .setAppointmentId(1001L)
                .setServiceId(2001L)
                .setPetId(3001L)
                .setLodgingId(4001L)
                .setStaffId(5001L)
                .setStartDate(
                        Date.newBuilder().setYear(2024).setMonth(3).setDay(15).build())
                .setStatus(AppointmentTaskStatus.INCOMPLETE)
                .setCareType(ServiceItemType.GROOMING)
                .build();

        // Act
        AppointmentTask result = AppointmentTaskConverter.INSTANCE.toEntity(request);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(2L);
        assertThat(result.getTaskCategory()).isEqualTo(AppointmentTaskCategory.MEDICATION.name());
        assertThat(result.getInstruction()).isEqualTo("Give medication");
        assertThat(result.getAppointmentId()).isEqualTo(1001L);
        assertThat(result.getServiceId()).isEqualTo(2001L);
        assertThat(result.getPetId()).isEqualTo(3001L);
        assertThat(result.getLodgingId()).isEqualTo(4001L);
        assertThat(result.getStaffId()).isEqualTo(5001L);
        assertThat(result.getStartDate()).isEqualTo(LocalDate.of(2024, 3, 15));
        assertThat(result.getStatus()).isEqualTo(AppointmentTaskStatus.INCOMPLETE.name());
        assertThat(result.getCareType()).isEqualTo(ServiceItemType.GROOMING_VALUE);

        // 验证可选字段为 null 或默认值
        assertThat(result.getStartTime()).isNull();
        assertThat(result.getTimeLabel()).isNull();
        assertThat(result.getDuration()).isNull();
        assertThat(result.getNoteContent()).isNull();
        assertThat(result.getNoteFeedback()).isNull();
    }

    @Test
    void testToEntity_WithOptionalFields() {
        // Arrange - 测试可选字段的处理
        UpdateAppointmentTaskRequest request = UpdateAppointmentTaskRequest.newBuilder()
                .setId(3L)
                .setTaskCategory(AppointmentTaskCategory.ADD_ONS)
                .setInstruction("Custom add-on task")
                .setAppointmentId(1002L)
                .setServiceId(2002L)
                .setPetId(3002L)
                .setLodgingId(4002L)
                .setStaffId(5002L)
                .setStartDate(
                        Date.newBuilder().setYear(2024).setMonth(4).setDay(10).build())
                .setStartTime(960) // 16:00 (4:00 PM)
                .setTimeLabel("Afternoon")
                .setDuration(60)
                .setStatus(AppointmentTaskStatus.COMPLETED)
                .setNoteContent("Task completed successfully")
                .setCareType(ServiceItemType.EVALUATION)
                .setNoteFeedback("Excellent work")
                .build();

        // Act
        AppointmentTask result = AppointmentTaskConverter.INSTANCE.toEntity(request);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(3L);
        assertThat(result.getTaskCategory()).isEqualTo(AppointmentTaskCategory.ADD_ONS.name());
        assertThat(result.getInstruction()).isEqualTo("Custom add-on task");
        assertThat(result.getAppointmentId()).isEqualTo(1002L);
        assertThat(result.getServiceId()).isEqualTo(2002L);
        assertThat(result.getPetId()).isEqualTo(3002L);
        assertThat(result.getLodgingId()).isEqualTo(4002L);
        assertThat(result.getStaffId()).isEqualTo(5002L);
        assertThat(result.getStartDate()).isEqualTo(LocalDate.of(2024, 4, 10));
        assertThat(result.getStartTime()).isEqualTo(960);
        assertThat(result.getTimeLabel()).isEqualTo("Afternoon");
        assertThat(result.getDuration()).isEqualTo(60);
        assertThat(result.getStatus()).isEqualTo(AppointmentTaskStatus.COMPLETED.name());
        assertThat(result.getNoteContent()).isEqualTo("Task completed successfully");
        assertThat(result.getCareType()).isEqualTo(ServiceItemType.EVALUATION_VALUE);
        assertThat(result.getNoteFeedback()).isEqualTo("Excellent work");
    }

    @Test
    void testEnumToInteger_WithValidEnum() {
        // Arrange
        ServiceItemType careType = ServiceItemType.BOARDING;

        // Act
        Integer result = AppointmentTaskConverter.INSTANCE.enumToInteger(careType);

        // Assert
        assertThat(result).isEqualTo(ServiceItemType.BOARDING_VALUE);
    }

    @Test
    void testEnumToInteger_WithNullEnum() {
        // Arrange
        ServiceItemType careType = null;

        // Act
        Integer result = AppointmentTaskConverter.INSTANCE.enumToInteger(careType);

        // Assert
        assertThat(result).isNull();
    }

    @Test
    void testIntegerToEnum_WithValidInteger() {
        // Arrange
        Integer careType = ServiceItemType.GROOMING_VALUE;

        // Act
        ServiceItemType result = AppointmentTaskConverter.INSTANCE.integerToEnum(careType);

        // Assert
        assertThat(result).isEqualTo(ServiceItemType.GROOMING);
    }

    @Test
    void testIntegerToEnum_WithNullInteger() {
        // Arrange
        Integer careType = null;

        // Act
        ServiceItemType result = AppointmentTaskConverter.INSTANCE.integerToEnum(careType);

        // Assert
        assertThat(result).isEqualTo(ServiceItemType.SERVICE_ITEM_TYPE_UNSPECIFIED);
    }
}
