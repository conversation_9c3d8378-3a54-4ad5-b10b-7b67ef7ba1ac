package com.moego.api.v3.appointment.converter;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.Timestamp;
import com.google.type.Date;
import com.google.type.Interval;
import com.moego.idl.api.appointment.v1.BatchUpdateAppointmentTaskParams;
import com.moego.idl.api.appointment.v1.ScheduleFilter;
import com.moego.idl.api.appointment.v1.UpdateAppointmentTaskParams;
import com.moego.idl.models.appointment.v1.AppointmentTaskCategory;
import com.moego.idl.models.appointment.v1.AppointmentTaskModel;
import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.models.appointment.v1.AppointmentTaskView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.BatchPatchAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.PatchAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentTaskRequest;
import java.time.Instant;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

class AppointmentTaskConverterTest {

    @Nested
    @DisplayName("toView 方法测试")
    class ToViewTests {

        @Test
        @DisplayName("应该正确转换 AppointmentTaskModel 到 AppointmentTaskView")
        void shouldConvertModelToViewCorrectly() {
            // Arrange
            var timestamp = Timestamp.newBuilder()
                    .setSeconds(Instant.parse("2024-01-01T10:00:00Z").getEpochSecond())
                    .build();
            var date = Date.newBuilder().setYear(2024).setMonth(1).setDay(15).build();

            var model = AppointmentTaskModel.newBuilder()
                    .setId(1L)
                    .setTaskCategory(AppointmentTaskCategory.FEEDING)
                    .setInstruction("Feed the pet")
                    .setAppointmentId(100L)
                    .setServiceId(200L)
                    .setPetId(300L)
                    .setLodgingId(400L)
                    .setStaffId(500L)
                    .setStartDate(date)
                    .setStartTime(600)
                    .setTimeLabel("10:00 AM")
                    .setDuration(30)
                    .setStatus(AppointmentTaskStatus.INCOMPLETE)
                    .setNoteContent("Some notes")
                    .setCreatedAt(timestamp)
                    .setUpdatedAt(timestamp)
                    .setDeletedAt(timestamp)
                    .setCompanyId(1000L)
                    .setBusinessId(2000L)
                    .setCareType(ServiceItemType.BOARDING)
                    .setNoteFeedback("Good job")
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toView(model);

            // Assert
            var expected = AppointmentTaskView.newBuilder()
                    .setId(1L)
                    .setTaskCategory(AppointmentTaskCategory.FEEDING)
                    .setInstruction("Feed the pet")
                    .setAppointmentId(100L)
                    .setServiceId(200L)
                    .setPetId(300L)
                    .setLodgingId(400L)
                    .setStaffId(500L)
                    .setStartDate(date)
                    .setStartTime(600)
                    .setTimeLabel("10:00 AM")
                    .setDuration(30)
                    .setStatus(AppointmentTaskStatus.INCOMPLETE)
                    .setNoteContent("Some notes")
                    .setCareType(ServiceItemType.BOARDING)
                    .setNoteFeedback("Good job")
                    .build();

            assertThat(result).isEqualTo(expected);
        }

        @Test
        @DisplayName("应该正确处理可选字段为空的情况")
        void shouldHandleOptionalFieldsCorrectly() {
            // Arrange
            var date = Date.newBuilder().setYear(2024).setMonth(2).setDay(20).build();

            var model = AppointmentTaskModel.newBuilder()
                    .setId(2L)
                    .setTaskCategory(AppointmentTaskCategory.MEDICATION)
                    .setInstruction("Give medication")
                    .setAppointmentId(101L)
                    .setServiceId(201L)
                    .setPetId(301L)
                    .setLodgingId(401L)
                    .setStaffId(501L)
                    .setStartDate(date)
                    .setStatus(AppointmentTaskStatus.COMPLETED)
                    .setCompanyId(1001L)
                    .setBusinessId(2001L)
                    .setCareType(ServiceItemType.GROOMING)
                    // 不设置可选字段
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toView(model);

            // Assert
            var expected = AppointmentTaskView.newBuilder()
                    .setId(2L)
                    .setTaskCategory(AppointmentTaskCategory.MEDICATION)
                    .setInstruction("Give medication")
                    .setAppointmentId(101L)
                    .setServiceId(201L)
                    .setPetId(301L)
                    .setLodgingId(401L)
                    .setStaffId(501L)
                    .setStartDate(date)
                    .setStatus(AppointmentTaskStatus.COMPLETED)
                    .setCareType(ServiceItemType.GROOMING)
                    .build();

            assertThat(result).isEqualTo(expected);
        }
    }

    @Nested
    @DisplayName("toRequest 方法测试 - UpdateAppointmentTaskParams")
    class ToRequestFromParamsTests {

        @Test
        @DisplayName("应该正确转换 UpdateAppointmentTaskParams 到 PatchAppointmentTaskRequest")
        void shouldConvertParamsToRequestCorrectly() {
            // Arrange
            var params = UpdateAppointmentTaskParams.newBuilder()
                    .setTaskId(1L)
                    .setStaffId(100L)
                    .setStatus(AppointmentTaskStatus.COMPLETED)
                    .setNoteContent("Task completed successfully")
                    .setNoteFeedback("Great work")
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toRequest(params);

            // Assert
            var expected = PatchAppointmentTaskRequest.newBuilder()
                    .setTaskId(1L)
                    .setStaffId(100L)
                    .setStatus(AppointmentTaskStatus.COMPLETED)
                    .setNoteContent("Task completed successfully")
                    .setNoteFeedback("Great work")
                    .build();

            assertThat(result).isEqualTo(expected);
        }

        @Test
        @DisplayName("应该正确处理可选字段为空的情况")
        void shouldHandleOptionalFieldsCorrectly() {
            // Arrange
            var params = UpdateAppointmentTaskParams.newBuilder()
                    .setTaskId(2L)
                    // 不设置可选字段
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toRequest(params);

            // Assert
            var expected =
                    PatchAppointmentTaskRequest.newBuilder().setTaskId(2L).build();

            assertThat(result).isEqualTo(expected);
        }
    }

    @Nested
    @DisplayName("toRequest 方法测试 - AppointmentTaskModel")
    class ToRequestFromModelTests {

        @Test
        @DisplayName("应该正确转换 AppointmentTaskModel 到 UpdateAppointmentTaskRequest")
        void shouldConvertModelToRequestCorrectly() {
            // Arrange
            var timestamp = Timestamp.newBuilder()
                    .setSeconds(Instant.parse("2024-01-01T10:00:00Z").getEpochSecond())
                    .build();
            var date = Date.newBuilder().setYear(2024).setMonth(3).setDay(10).build();

            var model = AppointmentTaskModel.newBuilder()
                    .setId(3L)
                    .setTaskCategory(AppointmentTaskCategory.ADD_ONS)
                    .setInstruction("Apply add-on service")
                    .setAppointmentId(102L)
                    .setServiceId(202L)
                    .setPetId(302L)
                    .setLodgingId(402L)
                    .setStaffId(502L)
                    .setStartDate(date)
                    .setStartTime(720)
                    .setTimeLabel("12:00 PM")
                    .setDuration(45)
                    .setStatus(AppointmentTaskStatus.INCOMPLETE)
                    .setNoteContent("Add-on notes")
                    .setCreatedAt(timestamp)
                    .setUpdatedAt(timestamp)
                    .setCareType(ServiceItemType.DAYCARE)
                    .setNoteFeedback("Needs attention")
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toRequest(model);

            // Assert
            var expected = UpdateAppointmentTaskRequest.newBuilder()
                    .setId(3L)
                    .setTaskCategory(AppointmentTaskCategory.ADD_ONS)
                    .setInstruction("Apply add-on service")
                    .setAppointmentId(102L)
                    .setServiceId(202L)
                    .setPetId(302L)
                    .setLodgingId(402L)
                    .setStaffId(502L)
                    .setStartDate(date)
                    .setStartTime(720)
                    .setTimeLabel("12:00 PM")
                    .setDuration(45)
                    .setStatus(AppointmentTaskStatus.INCOMPLETE)
                    .setNoteContent("Add-on notes")
                    .setCreatedAt(timestamp)
                    .setUpdatedAt(timestamp)
                    .setCareType(ServiceItemType.DAYCARE)
                    .setNoteFeedback("Needs attention")
                    .build();

            assertThat(result).isEqualTo(expected);
        }
    }

    @Nested
    @DisplayName("toFilter 方法测试")
    class ToFilterTests {

        @Test
        @DisplayName("应该正确转换 BatchUpdateAppointmentTaskParams.Filter 到 BatchPatchAppointmentTaskRequest.Filter")
        void shouldConvertFilterCorrectly() {
            // Arrange
            var startDate =
                    Date.newBuilder().setYear(2024).setMonth(4).setDay(1).build();
            var endDate = Date.newBuilder().setYear(2024).setMonth(4).setDay(30).build();

            var filter = BatchUpdateAppointmentTaskParams.Filter.newBuilder()
                    .addStatuses(AppointmentTaskStatus.INCOMPLETE)
                    .addStaffIds(100L)
                    .addStaffIds(200L)
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toFilter(filter);

            // Assert
            var expected = BatchPatchAppointmentTaskRequest.Filter.newBuilder()
                    .addStatuses(AppointmentTaskStatus.INCOMPLETE)
                    .addStaffIds(100L)
                    .addStaffIds(200L)
                    .build();

            assertThat(result).isEqualTo(expected);
        }

        @Test
        @DisplayName("应该正确处理空的过滤器")
        void shouldHandleEmptyFilterCorrectly() {
            // Arrange
            var filter = BatchUpdateAppointmentTaskParams.Filter.newBuilder().build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toFilter(filter);

            // Assert
            var expected = BatchPatchAppointmentTaskRequest.Filter.newBuilder().build();

            assertThat(result).isEqualTo(expected);
        }
    }

    @Nested
    @DisplayName("toRequest 方法测试 - BatchUpdateAppointmentTaskParams")
    class ToBatchRequestTests {

        @Test
        @DisplayName("应该正确转换带有所有字段的 BatchUpdateAppointmentTaskParams")
        void shouldConvertBatchParamsWithAllFieldsCorrectly() {
            // Arrange
            var date = Date.newBuilder().setYear(2024).setMonth(5).setDay(15).build();

            var filter = BatchUpdateAppointmentTaskParams.Filter.newBuilder()
                    .addStatuses(AppointmentTaskStatus.INCOMPLETE)
                    .build();

            var scheduleGroup = ScheduleFilter.newBuilder()
                    .setScheduleTime(600)
                    .setScheduleName("Morning Feed")
                    .build();

            var groupFilter1 = BatchUpdateAppointmentTaskParams.GroupFilter.newBuilder()
                    .setScheduleGroup(scheduleGroup)
                    .build();

            var groupFilter2 = BatchUpdateAppointmentTaskParams.GroupFilter.newBuilder()
                    .setCategoryGroup(AppointmentTaskCategory.MEDICATION)
                    .build();

            var groupFilter3 = BatchUpdateAppointmentTaskParams.GroupFilter.newBuilder()
                    .setAddOnId(1000L)
                    .build();

            var params = BatchUpdateAppointmentTaskParams.newBuilder()
                    .addTaskIds(1L)
                    .addTaskIds(2L)
                    .addTaskIds(3L)
                    .setStaffId(500L)
                    .setStatus(AppointmentTaskStatus.COMPLETED)
                    .setFilter(filter)
                    .addGroupFilters(groupFilter1)
                    .addGroupFilters(groupFilter2)
                    .addGroupFilters(groupFilter3)
                    .setDate(date)
                    .build();

            var dateInterval = Interval.newBuilder()
                    .setStartTime(com.google.protobuf.Timestamp.newBuilder()
                            .setSeconds(Instant.parse("2024-05-15T00:00:00Z").getEpochSecond())
                            .build())
                    .setEndTime(com.google.protobuf.Timestamp.newBuilder()
                            .setSeconds(Instant.parse("2024-05-15T23:59:59Z").getEpochSecond())
                            .build())
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toRequest(params, dateInterval);

            // Assert
            var expectedFilter = BatchPatchAppointmentTaskRequest.Filter.newBuilder()
                    .addStatuses(AppointmentTaskStatus.INCOMPLETE)
                    .setStartDateInterval(dateInterval)
                    .build();

            var expectedGroupFilter1 = BatchPatchAppointmentTaskRequest.GroupFilter.newBuilder()
                    .setStartTime(600)
                    .setTimeLabel("Morning Feed")
                    .build();

            var expectedGroupFilter2 = BatchPatchAppointmentTaskRequest.GroupFilter.newBuilder()
                    .setCategory(AppointmentTaskCategory.MEDICATION)
                    .build();

            var expectedGroupFilter3 = BatchPatchAppointmentTaskRequest.GroupFilter.newBuilder()
                    .setAddOnId(1000L)
                    .build();

            var expected = BatchPatchAppointmentTaskRequest.newBuilder()
                    .addTaskIds(1L)
                    .addTaskIds(2L)
                    .addTaskIds(3L)
                    .setStaffId(500L)
                    .setStatus(AppointmentTaskStatus.COMPLETED)
                    .setFilter(expectedFilter)
                    .addGroupFilters(expectedGroupFilter1)
                    .addGroupFilters(expectedGroupFilter2)
                    .addGroupFilters(expectedGroupFilter3)
                    .build();

            assertThat(result).isEqualTo(expected);
        }

        @Test
        @DisplayName("应该正确处理只有任务ID的最小参数")
        void shouldHandleMinimalParamsCorrectly() {
            // Arrange
            var params = BatchUpdateAppointmentTaskParams.newBuilder()
                    .addTaskIds(10L)
                    .addTaskIds(20L)
                    .build();

            var dateInterval = Interval.newBuilder()
                    .setStartTime(com.google.protobuf.Timestamp.newBuilder()
                            .setSeconds(Instant.parse("2024-06-01T00:00:00Z").getEpochSecond())
                            .build())
                    .setEndTime(com.google.protobuf.Timestamp.newBuilder()
                            .setSeconds(Instant.parse("2024-06-01T23:59:59Z").getEpochSecond())
                            .build())
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toRequest(params, dateInterval);

            // Assert
            var expected = BatchPatchAppointmentTaskRequest.newBuilder()
                    .addTaskIds(10L)
                    .addTaskIds(20L)
                    .build();

            assertThat(result).isEqualTo(expected);
        }

        @Test
        @DisplayName("应该正确处理带有日期但没有过滤器的情况")
        void shouldHandleDateWithoutFilterCorrectly() {
            // Arrange
            var date = Date.newBuilder().setYear(2024).setMonth(7).setDay(10).build();

            var params = BatchUpdateAppointmentTaskParams.newBuilder()
                    .addTaskIds(30L)
                    .setStaffId(600L)
                    .setDate(date)
                    .build();

            var dateInterval = Interval.newBuilder()
                    .setStartTime(com.google.protobuf.Timestamp.newBuilder()
                            .setSeconds(Instant.parse("2024-07-10T00:00:00Z").getEpochSecond())
                            .build())
                    .setEndTime(com.google.protobuf.Timestamp.newBuilder()
                            .setSeconds(Instant.parse("2024-07-10T23:59:59Z").getEpochSecond())
                            .build())
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toRequest(params, dateInterval);

            // Assert
            var expectedFilter = BatchPatchAppointmentTaskRequest.Filter.newBuilder()
                    .setStartDateInterval(dateInterval)
                    .build();

            var expected = BatchPatchAppointmentTaskRequest.newBuilder()
                    .addTaskIds(30L)
                    .setStaffId(600L)
                    .setFilter(expectedFilter)
                    .build();

            assertThat(result).isEqualTo(expected);
        }

        @Test
        @DisplayName("应该正确处理未知的组过滤器类型")
        void shouldHandleUnknownGroupFilterTypeCorrectly() {
            // Arrange
            var unknownGroupFilter = BatchUpdateAppointmentTaskParams.GroupFilter.newBuilder()
                    // 不设置任何 selectedGroup，这会导致 SELECTEDGROUP_NOT_SET
                    .build();

            var params = BatchUpdateAppointmentTaskParams.newBuilder()
                    .addTaskIds(40L)
                    .addGroupFilters(unknownGroupFilter)
                    .build();

            var dateInterval = Interval.newBuilder()
                    .setStartTime(com.google.protobuf.Timestamp.newBuilder()
                            .setSeconds(Instant.parse("2024-08-01T00:00:00Z").getEpochSecond())
                            .build())
                    .setEndTime(com.google.protobuf.Timestamp.newBuilder()
                            .setSeconds(Instant.parse("2024-08-01T23:59:59Z").getEpochSecond())
                            .build())
                    .build();

            // Act
            var result = AppointmentTaskConverter.INSTANCE.toRequest(params, dateInterval);

            // Assert
            var expected = BatchPatchAppointmentTaskRequest.newBuilder()
                    .addTaskIds(40L)
                    .build();

            assertThat(result).isEqualTo(expected);
        }
    }
}
