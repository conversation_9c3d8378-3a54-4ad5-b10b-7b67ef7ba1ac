syntax = "proto3";

package backend.proto.authn.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/authn/v1;authnpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.authn.v1";

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";


// MfaManagementService 提供管理用户多因素认证（MFA）设置的方法。
service MfaManagementService {
  // 列出指定用户注册的所有 MFA 认证方式。
  rpc ListAuthenticationFactors(ListAuthenticationFactorsRequest) returns (ListAuthenticationFactorsResponse);

  // 添加手机号作为 MFA 认证方式的第一步。
  // 此接口会向指定的手机号发送一个验证码，并返回一个 `verification_token` 用于第二步验证。
  rpc SendPhoneNumberVerificationCode(SendPhoneNumberVerificationCodeRequest) returns (SendPhoneNumberVerificationCodeResponse);

  // 添加手机号作为 MFA 认证方式的第二步。
  // 它会验证第一步中发送的验证码，并在成功后创建认证方式。
  rpc AddPhoneNumberFactor(AddPhoneNumberFactorRequest) returns (AddPhoneNumberFactorResponse);

  // 删除一个已存在的 MFA 认证方式。
  rpc DeleteAuthenticationFactor(DeleteAuthenticationFactorRequest) returns (google.protobuf.Empty);
}

// 代表一个已注册的认证方式的信息。
message AuthenticationFactor {
  // 针对 PHONE_NUMBER 类型的详细信息。
  message PhoneNumber {
    // E.164 格式的手机号 (例如, "+12223331234")。
    string e164_number = 1;
    // 手机号的区域代码 (例如, "US")。
    string region_code = 2;
  }

  // 此认证方式的唯一标识符。
  string id = 1;
  // 认证方式的类型。
  FactorType type = 2;

  // 特定类型的详细信息。
  oneof details {
    // 当类型为 PHONE_NUMBER 时，此字段将被填充。
    PhoneNumber phone_number = 4;
  }
}


// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We don't need pagination for now. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We don't need pagination for now. --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: We don't use parent field. --)
// ListAuthenticationFactors RPC 的请求消息。
message ListAuthenticationFactorsRequest {
  // 需要列出认证方式的用户账户 ID。
  int64 account_id = 1 [(buf.validate.field).int64.gt = 0];
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We don't need pagination for now. --)
// ListAuthenticationFactors RPC 的响应消息。
message ListAuthenticationFactorsResponse {
  // 为该用户注册的 MFA 认证方式列表。
  repeated AuthenticationFactor authentication_factors = 1;
}

// SendPhoneNumberVerificationCode RPC 的请求消息。
message SendPhoneNumberVerificationCodeRequest {
  // 用户的账户 ID。
  int64 account_id = 1 [(buf.validate.field).int64.gt = 0];
  // 将接收验证码的手机号码，E.164 格式（例如，+***********）。
  string e164_number = 2 [(buf.validate.field).cel = {
    id: "e164_number_format",
    message: "Please input a phone number in E.164 format.",
    expression: "this.matches('^\\\\+[1-9]\\\\d{1,14}$')"
  }];
  // 手机号的区域代码（例如，"US"）。
  string region_code = 3 [(buf.validate.field).string.len = 2];
}

// SendPhoneNumberVerificationCode RPC 的响应消息。
message SendPhoneNumberVerificationCodeResponse {
  // 代表此验证过程的临时一次性令牌。
  // 此令牌需要在 `AddPhoneNumberFactor` RPC 中使用。
  string verification_token = 1;
}

// AddPhoneNumberFactor RPC 的请求消息。
message AddPhoneNumberFactorRequest {
  // 要关联新认证方式的用户账户 ID。
  int64 account_id = 1 [(buf.validate.field).int64.gt = 0];
  // 手机号码的 E.164 格式。
  string e164_number = 2 [(buf.validate.field).cel = {
    id: "e164_number_format",
    message: "Please input a phone number in E.164 format.",
    expression: "this.matches('^\\\\+[1-9]\\\\d{1,14}$')"
  }];
  // 手机号的区域代码。
  string region_code = 3 [(buf.validate.field).string.len = 2];
  // 从 `SendPhoneNumberVerificationCode` 响应中获取的令牌。
  string verification_token = 4 [(buf.validate.field).string.min_len = 1];
  // 用户收到的 6 位数验证码。
  string verification_code = 5 [(buf.validate.field).string.len = 6];
  // 客户端的设备 ID。如果提供，该设备将在验证成功后被添加为受信任设备。
  optional string device_id = 6 [(buf.validate.field).string.min_len = 1];
}

// AddPhoneNumberFactor RPC 的响应消息。
message AddPhoneNumberFactorResponse {
  // 关于新创建的认证方式的信息。
  AuthenticationFactor authentication_factor = 1;
}

// DeleteAuthenticationFactor RPC 的请求消息。
message DeleteAuthenticationFactorRequest {
  // 要删除的认证方式的唯一标识符。
  string id = 1 [(buf.validate.field).string.min_len = 1];
}


// 定义多因素认证（MFA）方法的类型。
enum FactorType {
  // 未指定。
  FACTOR_TYPE_UNSPECIFIED = 0;
  // 基于手机号的认证（例如，短信）。
  PHONE_NUMBER = 1;
}
