package db

import (
	"context"
	"errors"

	"gorm.io/gorm"
)

type AuthenticationFactorReadWriter interface {
	Get(ctx context.Context, id int) error
	Create(ctx context.Context, factor *AuthenticationFactor) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, accountID int64) ([]*AuthenticationFactor, error)
	GetPhoneNumberFactor(ctx context.Context, accountID int64) (*AuthenticationFactor, error)
}

type authenticationFactorImpl struct {
	db *gorm.DB
}

func NewAuthenticationFactorReadWriter() AuthenticationFactorReadWriter {
	return &authenticationFactorImpl{
		db: newDB(),
	}
}

func (i *authenticationFactorImpl) Get(ctx context.Context, id int) error {
	return i.db.WithContext(ctx).Where("id = ?", id).First(&struct{}{}).Error
}

func (i *authenticationFactorImpl) Create(ctx context.Context, factor *AuthenticationFactor) error {
	return i.db.WithContext(ctx).Create(factor).Error
}

func (i *authenticationFactorImpl) Delete(ctx context.Context, id string) error {
	return i.db.WithContext(ctx).Delete(&AuthenticationFactor{}, "id = ?", id).Error
}

func (i *authenticationFactorImpl) List(ctx context.Context, accountID int64) ([]*AuthenticationFactor, error) {
	var factors []*AuthenticationFactor
	if err := i.db.WithContext(ctx).Where("account_id = ?", accountID).Find(&factors).Error; err != nil {
		return nil, err
	}

	return factors, nil
}

func (i *authenticationFactorImpl) GetPhoneNumberFactor(ctx context.Context,
	accountID int64) (*AuthenticationFactor, error) {
	var factor AuthenticationFactor
	err := i.db.WithContext(ctx).
		Where("account_id = ? AND type = ?", accountID, FactorTypePhoneNumber).
		First(&factor).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, err
	}

	return &factor, nil
}
