// Package repo 包含了所有外部资源的访问接口，比如数据库、缓存、RPC 等
// logic 层无需关注具体的实现，只需要知道接口即可，这样可以方便的进行单元测试

// 生成 mock 代码
//go:generate mockgen -source=./account/account.go -destination=./mock/account/account_mock.go -package=mockaccount
//go:generate mockgen -source=./cache/verification_code.go -destination=./mock/cache/verification_code_mock.go -package=mockcache
//go:generate mockgen -source=./db/authentication_factor.go -destination=./mock/db/authentication_factor_mock.go -package=mockdb
//go:generate mockgen -source=./db/trusted_device.go -destination=./mock/db/trusted_device_mock.go -package=mockdb
//go:generate mockgen -source=./message/message.go -destination=./mock/message/message_mock.go -package=mockmessage

package repo
