# 更新后的 BUILD.bazel 文件，已移除 greeter 相关内容
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "logic",
    srcs = [
        "authn_logic.go",
        "mfa_management_logic.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/logic",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/authn/config",
        "//backend/app/authn/repo/account",
        "//backend/app/authn/repo/cache",
        "//backend/app/authn/repo/db",
        "//backend/app/authn/repo/message",
        "//backend/app/authn/utils",
        "//backend/common/rpc/framework/log",
        "//backend/proto/authn/v1:authn",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/account/v1:account",
        "@org_golang_google_protobuf//types/known/durationpb",
    ],
)

go_test(
    name = "logic_test",
    srcs = [
        "authn_logic_test.go",
        "mfa_management_logic_test.go",
    ],
    embed = [":logic"],
    deps = [
        "//backend/app/authn/config",
        "//backend/app/authn/repo/cache",
        "//backend/app/authn/repo/db",
        "//backend/app/authn/repo/mock/account",
        "//backend/app/authn/repo/mock/cache",
        "//backend/app/authn/repo/mock/db",
        "//backend/app/authn/repo/mock/message",
        "//backend/app/authn/utils",
        "//backend/common/utils/pointer",
        "//backend/proto/authn/v1:authn",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/account/v1:account",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/account/v1:account",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_mock//gomock",
    ],
)
