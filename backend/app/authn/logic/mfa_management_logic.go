package logic

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/authn/repo/cache"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/message"
	authnutils "github.com/MoeGolibrary/moego/backend/app/authn/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	authnpb "github.com/MoeGolibrary/moego/backend/proto/authn/v1"
)

const (
	// nolint:lll
	// phoneNumberBindingContentTmpl is the SMS content for phone number binding verification.
	phoneNumberBindingContentTmpl = "[MoeGo] Your verification code is %s. Use this code to verify your phone number and enable two-factor authentication. Do not share this code with anyone."
)

type MfaManagementLogic struct {
	factorDB        db.AuthenticationFactorReadWriter
	trustedDeviceDB db.TrustedDeviceReadWriter
	codeCache       cache.VerificationCodeRepo
	messageAPI      message.API
}

// newMfaManagementLogic is the constructor for internal and test use.
// It accepts all dependencies as arguments, making it suitable for mocking.
func newMfaManagementLogic(
	factorDB db.AuthenticationFactorReadWriter,
	trustedDeviceDB db.TrustedDeviceReadWriter,
	codeCache cache.VerificationCodeRepo,
	messageAPI message.API,
) *MfaManagementLogic {
	return &MfaManagementLogic{
		factorDB:        factorDB,
		trustedDeviceDB: trustedDeviceDB,
		codeCache:       codeCache,
		messageAPI:      messageAPI,
	}
}

// NewMfaManagementLogic is the constructor for production use.
// It initializes dependencies from global sources (e.g., config.Get()).
func NewMfaManagementLogic() *MfaManagementLogic {
	codeCache, err := cache.NewVerificationCodeRepo()
	if err != nil {
		panic(err)
	}

	return newMfaManagementLogic(
		db.NewAuthenticationFactorReadWriter(),
		db.NewTrustedDeviceReadWriter(),
		codeCache,
		message.NewAPI(),
	)
}

func (l *MfaManagementLogic) ListAuthenticationFactors(ctx context.Context, accountID int64) (
	[]*authnpb.AuthenticationFactor, error) {
	factors, err := l.factorDB.List(ctx, accountID)
	if err != nil {
		return nil, err
	}

	var pbFactors []*authnpb.AuthenticationFactor
	for _, factor := range factors {
		pbFactor := &authnpb.AuthenticationFactor{
			Id: factor.ID,
		}

		switch factor.Type {
		case db.FactorTypePhoneNumber:
			pbFactor.Type = authnpb.FactorType_PHONE_NUMBER
			if details, ok := factor.ParsedDetails.(db.PhoneNumberDetails); ok {
				pbFactor.Details = &authnpb.AuthenticationFactor_PhoneNumber_{
					PhoneNumber: &authnpb.AuthenticationFactor_PhoneNumber{
						E164Number: details.E164Number,
						RegionCode: details.RegionCode,
					},
				}
			}
		default:
			pbFactor.Type = authnpb.FactorType_FACTOR_TYPE_UNSPECIFIED
		}
		pbFactors = append(pbFactors, pbFactor)
	}

	return pbFactors, nil
}

func (l *MfaManagementLogic) AddPhoneNumberFactor(ctx context.Context, req *authnpb.AddPhoneNumberFactorRequest) (
	*authnpb.AuthenticationFactor, error) {
	if !l.codeCache.Verify(ctx,
		cache.PurposePhoneNumberBinding, req.VerificationToken, req.VerificationCode, req.E164Number) {
		return nil, authnutils.VerificationCodeMismatchError
	}

	factor := &db.AuthenticationFactor{
		ID:        authnutils.NewUUIDv7(),
		AccountID: req.AccountId,
		Type:      db.FactorTypePhoneNumber,
		ParsedDetails: db.PhoneNumberDetails{
			E164Number: req.E164Number,
			RegionCode: req.RegionCode,
		},
	}

	if err := l.factorDB.Create(ctx, factor); err != nil {
		log.ErrorContextf(ctx, "failed to create phone number factor for account %d: %v", req.AccountId, err)

		return nil, authnutils.CreatePhoneNumberFactorError
	}

	// Upsert the device as a trusted device if device_id is provided.
	if deviceID := req.GetDeviceId(); len(deviceID) > 0 {
		device := &db.TrustedDevice{
			ID:        authnutils.NewUUIDv7(),
			AccountID: req.GetAccountId(),
			DeviceID:  deviceID,
		}
		if err := l.trustedDeviceDB.Upsert(ctx, device); err != nil {
			// Trusting a device is a non-critical operation.
			// If it fails, we should log the error but not fail the entire request.
			log.ErrorContextf(ctx, "failed to upsert trusted device for account %d: %v", req.GetAccountId(), err)
		}
	}

	return &authnpb.AuthenticationFactor{
		Id:   factor.ID,
		Type: authnpb.FactorType_PHONE_NUMBER,
		Details: &authnpb.AuthenticationFactor_PhoneNumber_{
			PhoneNumber: &authnpb.AuthenticationFactor_PhoneNumber{
				E164Number: req.E164Number,
				RegionCode: req.RegionCode,
			},
		},
	}, nil
}

// SendPhoneNumberVerificationCode sends a verification code to the user's phone number.
func (l *MfaManagementLogic) SendPhoneNumberVerificationCode(ctx context.Context,
	req *authnpb.SendPhoneNumberVerificationCodeRequest) (string, error) {
	token, code, err := l.codeCache.Create(ctx, cache.PurposePhoneNumberBinding, req.AccountId, req.E164Number)
	if err != nil {
		return "", err
	}

	content := fmt.Sprintf(phoneNumberBindingContentTmpl, code)
	if err := l.messageAPI.SendVerificationCode(ctx, req.RegionCode, req.E164Number, content); err != nil {
		return "", err
	}

	return token, nil
}

func (l *MfaManagementLogic) DeleteAuthenticationFactor(ctx context.Context, id string) error {
	return l.factorDB.Delete(ctx, id)
}
