load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "authn_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/authn/config",
        "//backend/app/authn/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/authn/v1:authn",
    ],
)

go_binary(
    name = "authn",
    embed = [":authn_lib"],
    visibility = ["//visibility:public"],
)
