package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/authn/logic"
	authnpb "github.com/MoeGolibrary/moego/backend/proto/authn/v1"
)

type AuthnService struct {
	authn *logic.AuthnLogic
	authnpb.UnimplementedAuthnServiceServer
}

func NewAuthnService() *AuthnService {
	return &AuthnService{
		authn: logic.NewAuthnLogic(),
	}
}

func (s *AuthnService) Login(ctx context.Context, req *authnpb.LoginRequest) (*authnpb.LoginResponse, error) {
	return s.authn.Login(ctx, req)
}

func (s *AuthnService) SendLoginChallengeCode(ctx context.Context,
	req *authnpb.SendLoginChallengeCodeRequest) (*authnpb.SendLoginChallengeCodeResponse, error) {
	token, err := s.authn.SendLoginChallengeCode(ctx, req)
	if err != nil {
		return nil, err
	}

	return &authnpb.SendLoginChallengeCodeResponse{
		ChallengeToken: token,
	}, nil
}
