load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = [
        "err.go",
        "utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/errs",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_grpc//codes",
    ],
)

go_test(
    name = "utils_test",
    srcs = ["utils_test.go"],
    embed = [":utils"],
    deps = ["@com_github_stretchr_testify//assert"],
)
