package authnutils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"math/big"

	"github.com/google/uuid"
)

// NewUUID returns a new UUID string.
func NewUUID() string {
	id := uuid.New()
	// 直接把 16 个字节编码成 32 位 hex 字符串
	buf := make([]byte, 32)
	hex.Encode(buf, id[:])

	return string(buf)
}

// NewUUIDv7 returns a new ordered UUID (v7) string.
func NewUUIDv7() string {
	id, _ := uuid.NewV7()
	// 直接把 16 个字节编码成 32 位 hex 字符串
	buf := make([]byte, 32)
	hex.Encode(buf, id[:])

	return string(buf)
}

var (
	weakCodes = map[int64]struct{}{
		0:      {},
		111111: {},
		222222: {},
		333333: {},
		444444: {},
		555555: {},
		666666: {},
		777777: {},
		888888: {},
		999999: {},
		123456: {},
	}
)

// New6DigitCode returns a random 6 digit code. e.g.: 342412
func New6DigitCode() string {
	count := 0
	for {
		value := RandInt(1000000)
		// 避免生成弱号码
		if _, isWeak := weakCodes[value]; !isWeak {
			return fmt.Sprintf("%06d", value)
		}
		// 上述过程中 err 都被忽略了，为了避免发生错误陷入死循环，这里用 panic 兜底
		if count++; count > 1000 {
			panic("failed to generate random code")
		}
	}
}

// RandInt returns a random int64 in [0, max).
// max must be positive (>0).
func RandInt(maxValue int64) int64 {
	v, _ := rand.Int(rand.Reader, big.NewInt(maxValue))

	return v.Int64()
}
