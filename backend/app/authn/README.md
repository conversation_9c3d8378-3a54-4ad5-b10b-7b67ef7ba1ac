# Authn 服务端到端文档

本文档是 `authn` 服务的统一说明，旨在通过一个端到端的视角，帮助 API 调用者和内部维护者理解其完整的认证流程——从客户端发起的 API 请求，到服务内部的业务逻辑、数据流和安全实现。

> **API 详细定义**: 关于每个 RPC 的具体请求/响应字段和详细注释，请直接参考 `backend/proto/authn/v1/` 目录下的 `.proto` 文件。

---

## 1. 端到端登录流程

### 1.1. 流程全景图

下图展示了从客户端发起登录请求，到服务内部与数据库、缓存及其他微服务交互，直至最终完成登录的完整流程。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Authn as Authn 服务
    participant Account as Account 服务
    participant MessageHub as Message hub 服务
    participant Redis
    participant DB as 数据库

    Client->>Authn: 1. 调用 `Login` RPC (email, password, device_id 等参数)
    Authn->>Account: 2. 校验邮箱和密码

    alt 凭证无效
        Account-->>Authn: 凭证无效
        Authn-->>Client: 返回“账号或密码错误”
    end

    Account-->>Authn: 3. 凭证有效
    Authn->>DB: 4. 检查是否配置 MFA (手机)
    alt 未配置 MFA
        Authn->>Account: 5a. 创建会话
        Account-->>Authn: 6a. 返回 session_token
        Authn-->>Client: 7a. `Login` 成功，返回 session_token
    else 已配置 MFA
        Authn->>DB: 5b. 检查是否为可信设备 (device_id)
        alt 是可信设备 (90天内验证过)
             Authn->>Account: 6b. 创建会话
             Account-->>Authn: 7b. 返回 session_token
             Authn-->>Client: 8b. `Login` 成功，返回 session_token
        else 是新设备或设备已过期
            Authn-->>Client: 6c. `Login` 响应 `require_mfa=true`
            
            Client->>Authn: 7c. 调用 `SendLoginChallengeCode` RPC
            Authn->>Redis: 8c. 请求生成验证码 (触发频率限制检查)
            alt 触发频率限制 (1分钟内超过n次请求)
                Redis-->>Authn: 生成失败
                Authn-->>Client: 返回“请求过于频繁”
            else 频率正常
                Redis-->>Authn: 9c. 生成 code 和 challenge_token
                Authn->>MessageHub: 10c. 调用接口发送短信
                Authn-->>Client: 11c. `SendLoginChallengeCode` 成功，返回 `challenge_token`
            end
        end
    end

    opt 用户未收到或验证码过期
        Client->>Authn: (可选) 再次调用 `SendLoginChallengeCode`
        Authn->>Redis: 再次请求生成验证码 (触发频率限制检查)
        alt 触发频率限制
            Redis-->>Authn: 生成失败
            Authn-->>Client: 返回“请求过于频繁”
        else 频率正常
            Redis-->>Authn: 生成新的 code 和 challenge_token
            Authn->>MessageHub: 再次调用接口发送短信
            Authn-->>Client: 返回新的 challenge_token
        end
    end

    Client->>Authn: 12. 再次调用 `Login` RPC (携带 challenge_token, challenge_code)
    Authn->>Redis: 13. 校验验证码 (触发防暴力破解检查)

    alt 验证码错误或已过期
        Redis-->>Authn: 校验失败 (尝试次数 +1)
        Authn-->>Client: 返回“验证码错误”
        Note right of Redis: 尝试次数 > 5, 验证码将被删除
    else 验证码正确
        Redis-->>Authn: 14. 校验成功
        Authn->>DB: 15. 更新设备为可信
        Authn->>Account: 16. 创建会话
        Account-->>Authn: 17. 返回 session_token
        Authn-->>Client: 18. `Login` 成功，返回 session_token
    end

```

### 1.2. 详细步骤解析

1.  **首次登录尝试 (凭证验证)**:
    *   **外部**: 客户端调用 `Login(email, password, ...)` RPC 发起登录请求。
    *   **内部**: `Authn` 服务接收到请求后，首先将凭证转发给 `Account` 服务进行验证。如果失败，流程立即终止并向客户端返回错误。

2.  **MFA 检查**:
    *   **内部**: 凭证验证成功后，服务查询数据库，检查用户是否配置了 MFA 以及当前设备是否为“可信设备”（90天内验证过）。
    *   **外部**:
        *   如果**无需 MFA**（未配置或设备可信），`Authn` 服务会直接请求 `Account` 服务创建会话，并将 `session_token` 在**首次 `Login` RPC 的响应中**直接返回给客户端，登录成功。
        *   如果**需要 MFA**，`Login` RPC 的首次响应将返回 `require_mfa = true` 和 `factor`，且**不包含 `session_token`**。

3.  **MFA 质询 (发送验证码)**:
    *   **外部**: 客户端收到 `require_mfa = true` 的响应后，必须调用 `SendLoginChallengeCode()` RPC 来请求发送验证码。
    *   **内部**: `Authn` 服务调用 `repo/cache` 在 Redis 中生成验证码和 `challenge_token`（此操作受频率限制），然后通过 `Message hub 服务` 发送短信。
    *   **外部**: `SendLoginChallengeCode` RPC 成功后，向客户端返回 `challenge_token`。

4.  **二次登录尝试 (MFA 验证)**:
    *   **外部**: 用户输入验证码后，客户端**再次调用 `Login` RPC**，但这次请求体中必须携带上一步获取的 `challenge_token` 和用户输入的 `challenge_code`。
    *   **内部**: `Authn` 服务调用 `repo/cache` 校验验证码（此操作受防暴力破解保护）。校验成功后，服务会将当前设备更新为可信，并请求 `Account` 服务创建会话。
    *   **外部**: `Authn` 服务在**第二次 `Login` RPC 的响应中**返回 `session_token`，登录成功。

---

## 2. 端到端 MFA 管理流程

### 2.1. 添加手机号流程全景图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Authn as Authn 服务
    participant MessageHub as Message hub 服务
    participant Redis
    participant DB as 数据库

    Client->>Authn: 1. 调用 `SendPhoneNumberVerificationCode` RPC
    Authn->>Redis: 2. 请求生成验证码 (类型: 绑定手机)
    alt 触发频率限制
        Redis-->>Authn: 生成失败
        Authn-->>Client: 返回“请求过于频繁”
    else 频率正常
        Redis-->>Authn: 3. 生成 code 和 verification_token
        Authn->>MessageHub: 4. 调用接口发送短信
        Authn-->>Client: 5. `SendPhoneNumberVerificationCode` 成功，返回 `verification_token`
    end

    opt 用户未收到或验证码过期
        Client->>Authn: (可选) 再次调用 `SendPhoneNumberVerificationCode`
        Authn->>Redis: 再次请求生成验证码 (触发频率限制检查)
        alt 触发频率限制
            Redis-->>Authn: 生成失败
            Authn-->>Client: 返回“请求过于频繁”
        else 频率正常
            Redis-->>Authn: 生成新的 code 和 verification_token
            Authn->>MessageHub: 再次调用接口发送短信
            Authn-->>Client: 返回新的 verification_token
        end
    end

    Client->>Authn: 6. 调用 `AddPhoneNumberFactor` RPC (verification_token, code, device_id?)
    Authn->>Redis: 7. 校验验证码
    alt 验证码错误或过期
        Redis-->>Authn: 校验失败
        Authn-->>Client: 返回“验证码错误”
    else 验证码正确
        Redis-->>Authn: 8. 校验成功
        Authn->>DB: 9. 创建 `authentication_factor` 记录
        
        opt 如果提供了 device_id
            Authn->>DB: 10. (可选) 将设备设为可信
        end

        Authn-->>Client: 11. `AddPhoneNumberFactor` 成功，返回新创建的 factor
    end
```

### 2.2. 详细步骤解析

1.  **请求发送验证码**:
    *   **外部**: 这是添加手机号的第一步。客户端调用 `SendPhoneNumberVerificationCode(account_id, phone_number)` RPC。
    *   **内部**: 服务在 Redis 中生成验证码和 `verification_token`（受频率限制），并通过 `Message hub 服务` 发送短信。
    *   **外部**: RPC 成功后，向客户端返回 `verification_token`。

2.  **验证并添加手机**:
    *   **外部**: 用户输入验证码后，客户端调用 `AddPhoneNumberFactor(..., verification_token, code)` RPC。
    *   **内部**: 服务使用 `verification_token` 在 Redis 中校验验证码（受防暴力破解保护）。成功后，在数据库中创建 `authentication_factor` 记录。如果请求中包含了 `device_id`，则会顺便将该设备设为可信。
    *   **外部**: RPC 成功后，向客户端返回新创建的 `factor` 信息。

---

## 3. 核心组件

`authn` 服务主要由以下几个部分构成：

-   **`AuthnLogic`**: 负责处理用户的核心登录流程。它协调账户验证、MFA 检查、MFA 质询以及最终的会话创建。
-   **`MfaManagementLogic`**: 负责管理用户 MFA 认证方式的生命周期，包括添加、列出和删除手机号等认证方式。
-   **`repo/account`**: 作为 `account` 服务的客户端代理，用于验证用户凭证、创建会话等。
-   **`repo/db`**: 负责与数据库交互，持久化存储 `authentication_factor`（MFA 认证方式）和 `trusted_device`（可信设备）等信息。
-   **`repo/cache`**: 负责验证码的生成、存储和校验。它基于 Redis 实现，并包含频率限制和防暴力破解等关键安全机制。

---

## 4. 关键机制：验证码与安全策略

验证码系统是 `authn` 服务安全的核心，通过 `repo/cache/verification_code.go` 中的 Redis Lua 脚本实现。

-   **原子性**: 所有对 Redis 的读、改、写操作都在一个 Lua 脚本中完成，保证了操作的原子性，避免了竞态条件。
-   **频率限制 (Rate Limiting)**: 在生成验证码时进行检查，防止短信轰炸。
-   **防暴力破解 (Anti-Brute-Force)**: 在校验验证码时进行检查，输错 5 次后令牌即失效。
-   **有效期 (Expiration)**: 所有验证码令牌的有效期为 5 分钟。

---

## 5. 外部依赖

-   **Account 服务**: 核心依赖，负责用户身份的基础管理。
-   **Message hub 服务**: MFA 流程的核心依赖，负责发送短信。
-   **Organization 服务**: 业务流程依赖，用于同步用户信息到 CRM。
-   **Business 服务**: 业务流程依赖，用于初始化特定业务会话。