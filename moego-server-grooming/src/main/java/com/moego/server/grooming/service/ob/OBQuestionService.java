package com.moego.server.grooming.service.ob;

import com.moego.common.enums.QuestionConst;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BusinessCustomerReferralSourceServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListCustomerReferralSourceRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffSimpleDTO;
import com.moego.server.customer.api.ICustomerNoteService;
import com.moego.server.customer.api.IPetNoteService;
import com.moego.server.customer.dto.CustomerSourceDto;
import com.moego.server.customer.params.CustomerNoteSaveVo;
import com.moego.server.customer.params.PetNoteSaveVo;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.mapper.MoeBookOnlineQuestionMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineQuestionSaveMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionSave;
import com.moego.server.grooming.mapstruct.BookOnlineQuestionConverter;
import com.moego.server.grooming.mapstruct.QuestionMapper;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.util.CustomQuestionUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.function.SingletonSupplier;

/**
 * <AUTHOR>
 * @since 2022/10/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBQuestionService {

    private final MoeBookOnlineQuestionMapper questionMapper;
    private final MoeBookOnlineQuestionSaveMapper questionSaveMapper;
    private final ICustomerNoteService customerNoteService;
    private final IPetNoteService petNoteService;
    private final BusinessInfoHelper businessInfoHelper;
    private final MoeBusinessBookOnlineMapper bookOnlineMapper;
    private final BusinessCustomerReferralSourceServiceGrpc.BusinessCustomerReferralSourceServiceBlockingStub
            businessCustomerReferralSourceServiceBlockingStub;
    private final ApplicationContext applicationContext;
    /**
     * 这里的依赖关系太逆天了，直接 inject 会导致循环依赖，这里使用懒加载解决这个问题。
     */
    private final SingletonSupplier<OBBusinessStaffService> obBusinessStaffServiceSupplier =
            SingletonSupplier.of(this::getOBBusinessStaffService);

    private static final String REFERRAL_SOURCE = "Referral source";
    private static final String PREFERRED_GROOMER = "Preferred groomer";

    public List<GroomingQuestionDTO> getAllQuestionsByBusinessId(Integer businessId) {
        return getQuestionsByBusinessId(businessId, null);
    }

    public List<GroomingQuestionDTO> getQuestionsByBusinessId(Integer businessId, Integer type) {
        List<MoeBookOnlineQuestion> originQuestion = questionMapper.getListByBusinessId(businessId, type);
        List<GroomingQuestionDTO> clientQuestions = new ArrayList<>();
        originQuestion.forEach(question -> {
            var questionDTO = BookOnlineQuestionConverter.entityToDTO(question);
            if (QuestionConst.IS_ALLOW_DELETE_FALSE.equals(questionDTO.getIsAllowDelete())) {
                questionDTO.setKey(question.getQuestion().replace(" ", "_"));
            } else {
                questionDTO.setKey(QuestionConst.KEY_PREFIX + question.getId());
            }

            if (REFERRAL_SOURCE.equals(question.getQuestion()) || PREFERRED_GROOMER.equals(question.getQuestion())) {
                questionDTO.setQuestionType(QuestionConst.QUESTION_TYPE_SHORT);
            }
            clientQuestions.add(questionDTO);
        });
        return clientQuestions;
    }

    public List<GroomingQuestionDTO> getShowQuestionsByBusinessId(
            boolean migrated, long companyId, Integer businessId, Integer type) {
        return questionMapper.getShowQuestionsByBusinessId(businessId, type).stream()
                .map(question -> {
                    var questionDTO = BookOnlineQuestionConverter.entityToDTO(question);
                    if (QuestionConst.IS_ALLOW_DELETE_FALSE.equals(questionDTO.getIsAllowDelete())) {
                        questionDTO.setKey(question.getQuestion().replace(" ", "_"));
                    } else {
                        questionDTO.setKey(QuestionConst.KEY_PREFIX + question.getId());
                    }

                    if (REFERRAL_SOURCE.equals(question.getQuestion())) {
                        var request =
                                ListCustomerReferralSourceRequest.newBuilder().setCompanyId(companyId);
                        if (!migrated) {
                            request.setBusinessId(businessId);
                        }

                        var sourceList =
                                businessCustomerReferralSourceServiceBlockingStub
                                        .listCustomerReferralSource(request.build())
                                        .getReferralSourcesList()
                                        .stream()
                                        .map(source -> {
                                            var dto = new CustomerSourceDto();
                                            dto.setId((int) source.getId());
                                            dto.setSourceName(source.getName());
                                            return dto;
                                        });

                        questionDTO.setExtraJson(JsonUtil.toJson(sourceList));
                    } else if (PREFERRED_GROOMER.equals(question.getQuestion())) {
                        List<StaffSimpleDTO> staffList = listOBAvailableStaff(businessId).stream()
                                .map(dto -> StaffSimpleDTO.builder()
                                        .id(dto.getId())
                                        .firstName(dto.getFirstName())
                                        .lastName(dto.getLastName())
                                        .build())
                                .toList();
                        questionDTO.setExtraJson(JsonUtil.toJson(staffList));
                    }
                    return questionDTO;
                })
                .toList();
    }

    private List<MoeStaffDto> listOBAvailableStaff(Integer businessId) {
        var bookOnline = Optional.ofNullable(bookOnlineMapper.selectByBusinessId(businessId))
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "OB setting not found for business: " + businessId));
        return obBusinessStaffServiceSupplier
                .obtain()
                .getOBAvailableStaffList(businessId, bookOnline.getAvailableTimeType());
    }

    /**
     * Get custom question map, allowed to delete
     *
     * @param businessId business id
     * @return Map<String, MoeBookOnlineQuestion> key: custom_{id}, value: MoeBookOnlineQuestion
     */
    public Map<String, MoeBookOnlineQuestion> getCustomQuestionMap(Integer businessId) {
        List<MoeBookOnlineQuestion> questionList = questionMapper.getListByBusinessId(businessId, null);
        return questionList.stream()
                .filter(question -> Objects.equals(QuestionConst.IS_ALLOW_DELETE_TRUE, question.getIsAllowDelete()))
                .collect(
                        Collectors.toMap(question -> QuestionConst.KEY_PREFIX + question.getId(), Function.identity()));
    }

    /**
     * Get existing client latest question answer
     *
     * @param businessId business id
     * @param customerId customer id
     * @return question answer dto
     */
    public BookOnlineQuestionSaveDTO getCustomerLatestQuestionSave(Integer businessId, Integer customerId) {
        List<MoeBookOnlineQuestionSave> saveList =
                questionSaveMapper.selectByCustomerIdWithPetIds(businessId, customerId);
        return QuestionMapper.INSTANCE.buildCustomerQuestionSaveDTO(businessId, customerId, saveList);
    }

    public List<BookOnlineQuestionSaveDTO> listCustomerLatestQuestionSave(
            Integer businessId, List<Integer> customerIds) {
        List<MoeBookOnlineQuestionSave> saveList =
                questionSaveMapper.selectByCustomerIdsWithPetIds(businessId, customerIds);
        Map<Integer, List<MoeBookOnlineQuestionSave>> customerQuestionMap =
                saveList.stream().collect(Collectors.groupingBy(MoeBookOnlineQuestionSave::getCustomerId));
        return customerQuestionMap.entrySet().stream()
                .map(entry -> QuestionMapper.INSTANCE.buildCustomerQuestionSaveDTO(
                        businessId, entry.getKey(), entry.getValue()))
                .toList();
    }

    public Boolean saveCustomerQuestionSave(BookOnlineQuestionSaveDTO questionSaveDTO) {
        List<GroomingQuestionDTO> questions = this.getAllQuestionsByBusinessId(questionSaveDTO.getBusinessId());
        Map<String, Object> customQuestionMap = questionSaveDTO.getClientCustomQuestionMap();
        if (!CollectionUtils.isEmpty(customQuestionMap)) {
            MoeBookOnlineQuestionSave questionSave = new MoeBookOnlineQuestionSave();
            questionSave.setBusinessId(questionSaveDTO.getBusinessId());
            questionSave.setCompanyId(questionSaveDTO.getCompanyId());
            questionSave.setCustomerId(questionSaveDTO.getCustomerId());
            questionSave.setType(QuestionConst.TYPE_PET_OWNER_QUESTION);
            questionSave.setQuestionJson(JsonUtil.toJson(customQuestionMap));
            questionSave.setCreateTime(DateUtil.get10Timestamp());
            questionSave.setUpdateTime(DateUtil.get10Timestamp());
            questionSaveMapper.insertSelective(questionSave);
            CustomerNoteSaveVo saveVo = new CustomerNoteSaveVo();
            saveVo.setCustomerId(questionSaveDTO.getCustomerId());
            saveVo.setNote(CustomQuestionUtil.generateNote(questions, customQuestionMap));
            customerNoteService.createIdempotentCustomerNote(questionSaveDTO.getBusinessId(), null, saveVo);
        }
        Map<Integer, Map<String, Object>> petCustomQuestionMap = questionSaveDTO.getPetCustomQuestionMap();
        if (!CollectionUtils.isEmpty(petCustomQuestionMap)) {
            petCustomQuestionMap.forEach((petId, petQuestionMap) -> {
                MoeBookOnlineQuestionSave questionSave = new MoeBookOnlineQuestionSave();
                questionSave.setBusinessId(questionSaveDTO.getBusinessId());
                questionSave.setCompanyId(questionSaveDTO.getCompanyId());
                questionSave.setCustomerId(questionSaveDTO.getCustomerId());
                questionSave.setPetId(petId);
                questionSave.setType(QuestionConst.TYPE_PET_QUESTION);
                questionSave.setQuestionJson(JsonUtil.toJson(petQuestionMap));
                questionSave.setCreateTime(DateUtil.get10Timestamp());
                questionSave.setUpdateTime(DateUtil.get10Timestamp());
                questionSaveMapper.insertSelective(questionSave);
                PetNoteSaveVo saveVo = new PetNoteSaveVo();
                saveVo.setPetId(petId);
                saveVo.setNote(CustomQuestionUtil.generateNote(questions, petQuestionMap));
                petNoteService.insertIdempotentPetNote(null, saveVo);
            });
        }
        return Boolean.TRUE;
    }

    public void upsertCustomerQuestionSave(
            BookOnlineQuestionSaveDTO questionSaveDTO,
            List<GroomingQuestionDTO> questions,
            BookOnlineQuestionSaveDTO latestQuestionSave,
            Boolean autoAcceptConflict) {
        MoeBookOnlineQuestionSave questionSave = new MoeBookOnlineQuestionSave();
        questionSave.setBusinessId(questionSaveDTO.getBusinessId());
        questionSave.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(questionSaveDTO.getBusinessId()));
        questionSave.setCustomerId(questionSaveDTO.getCustomerId());
        questionSave.setType(QuestionConst.TYPE_PET_OWNER_QUESTION);
        Map<String, Object> clientQuestions = QuestionMapper.INSTANCE.mergeCustomQuestion(
                latestQuestionSave.getClientCustomQuestionMap(),
                questionSaveDTO.getClientCustomQuestionMap(),
                autoAcceptConflict);
        questionSave.setQuestionJson(JsonUtil.toJson(clientQuestions));
        if (CollectionUtils.isEmpty(latestQuestionSave.getClientCustomQuestionMap())) {
            questionSave.setCreateTime(DateUtil.get10Timestamp());
            questionSave.setUpdateTime(DateUtil.get10Timestamp());
            questionSaveMapper.insertSelective(questionSave);
        } else {
            questionSave.setUpdateTime(DateUtil.get10Timestamp());
            questionSaveMapper.updateByCustomerIdOrPetId(questionSave);
        }
        Map<String, Object> noteQuestion = QuestionMapper.INSTANCE.filterNoteQuestion(
                latestQuestionSave.getClientCustomQuestionMap(),
                questionSaveDTO.getClientCustomQuestionMap(),
                autoAcceptConflict);
        if (CollectionUtils.isEmpty(noteQuestion)) {
            return;
        }
        CustomerNoteSaveVo saveVo = new CustomerNoteSaveVo();
        saveVo.setCustomerId(questionSaveDTO.getCustomerId());
        saveVo.setNote(CustomQuestionUtil.generateNote(questions, questionSaveDTO.getClientCustomQuestionMap()));
        customerNoteService.createIdempotentCustomerNote(
                questionSaveDTO.getBusinessId(), AuthContext.get().getStaffId(), saveVo);
    }

    public void upsertPetQuestionSave(
            BookOnlineQuestionSaveDTO questionSaveDTO,
            List<GroomingQuestionDTO> questions,
            BookOnlineQuestionSaveDTO latestQuestionSave,
            Boolean autoAcceptConflict) {
        questionSaveDTO.getPetCustomQuestionMap().forEach((petId, petQuestionMap) -> {
            if (CollectionUtils.isEmpty(petQuestionMap)) {
                return;
            }
            MoeBookOnlineQuestionSave questionSave = new MoeBookOnlineQuestionSave();
            questionSave.setBusinessId(questionSaveDTO.getBusinessId());
            questionSave.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(questionSaveDTO.getBusinessId()));
            questionSave.setCustomerId(questionSaveDTO.getCustomerId());
            questionSave.setPetId(petId);
            questionSave.setType(QuestionConst.TYPE_PET_QUESTION);
            Map<String, Object> existingQuestions =
                    latestQuestionSave.getPetCustomQuestionMap().get(petId);
            questionSave.setQuestionJson(JsonUtil.toJson(QuestionMapper.INSTANCE.mergeCustomQuestion(
                    existingQuestions, petQuestionMap, autoAcceptConflict)));
            if (CollectionUtils.isEmpty(existingQuestions)) {
                questionSave.setCreateTime(DateUtil.get10Timestamp());
                questionSave.setUpdateTime(DateUtil.get10Timestamp());
                questionSaveMapper.insertSelective(questionSave);
            } else {
                questionSave.setUpdateTime(DateUtil.get10Timestamp());
                questionSaveMapper.updateByCustomerIdOrPetId(questionSave);
            }
            Map<String, Object> noteQuestion =
                    QuestionMapper.INSTANCE.filterNoteQuestion(existingQuestions, petQuestionMap, autoAcceptConflict);
            if (CollectionUtils.isEmpty(noteQuestion)) {
                return;
            }
            PetNoteSaveVo saveVo = new PetNoteSaveVo();
            saveVo.setPetId(petId);
            saveVo.setNote(CustomQuestionUtil.generateNote(questions, petQuestionMap));
            petNoteService.insertIdempotentPetNote(AuthContext.get().getStaffId(), saveVo);
        });
    }

    /**
     * Update customer latest question save
     *
     * @param questionSaveDTO    client question answers and pet question answers
     * @param autoAcceptConflict auto accept conflict field
     */
    @Transactional
    public void upsertCustomerLatestQuestionSave(
            BookOnlineQuestionSaveDTO questionSaveDTO, Boolean autoAcceptConflict) {
        List<GroomingQuestionDTO> questions = this.getAllQuestionsByBusinessId(questionSaveDTO.getBusinessId());
        BookOnlineQuestionSaveDTO latestQuestionSave =
                getCustomerLatestQuestionSave(questionSaveDTO.getBusinessId(), questionSaveDTO.getCustomerId());
        if (!CollectionUtils.isEmpty(questionSaveDTO.getClientCustomQuestionMap())) {
            upsertCustomerQuestionSave(questionSaveDTO, questions, latestQuestionSave, autoAcceptConflict);
        }
        if (!CollectionUtils.isEmpty(questionSaveDTO.getPetCustomQuestionMap())) {
            upsertPetQuestionSave(questionSaveDTO, questions, latestQuestionSave, autoAcceptConflict);
        }
    }

    private OBBusinessStaffService getOBBusinessStaffService() {
        return applicationContext.getBean(OBBusinessStaffService.class);
    }
}
