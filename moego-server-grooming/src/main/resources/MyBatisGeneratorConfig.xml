<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC
  "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- use mbGenerator: https://moego.atlassian.net/wiki/spaces/~************************/pages/79233368 -->
<generatorConfiguration>
  <context id="Mysql" targetRuntime="MyBatis3" defaultModelType="flat">
    <property name="beginningDelimiter" value="`"/>
    <property name="endingDelimiter" value="`"/>
    <property name="javaFileEncoding" value="UTF-8"/>

    <plugin type="com.moego.lib.common.mybatisplugins.DeprecatedColumnsPlugin"/>

    <commentGenerator>
      <property name="suppressDate" value="true"/>
      <property name="addRemarkComments" value="true"/>
    </commentGenerator>

    <jdbcConnection
      driverClass="com.mysql.jdbc.Driver"
      connectionURL="***************************************************************"
      userId="moego_developer_240310_eff7a0dc"
      password="G0MxI7NM_jX_f7Ky73vnrwej97xg1tly"
    >
    </jdbcConnection>

    <!-- 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer，为 true时把JDBC DECIMAL 和
                NUMERIC 类型解析为java.math.BigDecimal -->
    <!--    <javaTypeResolver>-->
    <!--      <property name="forceBigDecimals" value="true"/>-->
    <!--      &lt;!&ndash; This property is used to specify whether MyBatis Generator should force the use of JSR-310 data types for DATE, TIME,-->
    <!--      and TIMESTAMP fields, rather than using java.util.Date &ndash;&gt;-->
    <!--      <property name="useJSR310Types" value="true"/>-->
    <!--    </javaTypeResolver>-->

    <javaModelGenerator
      targetPackage="com.moego.server.grooming.mapperbean"
      targetProject="moego-server-grooming/src/main/java"
    >
      <property name="trimStrings" value="true"/>
    </javaModelGenerator>

    <sqlMapGenerator
      targetPackage="com.moego.server.grooming.mapperxml"
      targetProject="moego-server-grooming/src/main/java"
    />

    <javaClientGenerator
      targetPackage="com.moego.server.grooming.mapper"
      targetProject="moego-server-grooming/src/main/java"
      type="XMLMAPPER"
    >
    </javaClientGenerator>

    <!-- <table tableName="moe_grooming_appointment"
           enableCountByExample="true"
           enableSelectByExample="true"
           enableUpdateByExample="true">
      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
      <columnOverride column="is_deprecate" javaType="Integer" jdbcType="BOOLEAN"/>
      <columnOverride column="is_block" javaType="Integer" jdbcType="BOOLEAN"/>
      <columnOverride column="status_before_checkin"
                      javaType="com.moego.server.grooming.enums.AppointmentStatusEnum"
                      typeHandler="com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"/>
      <columnOverride column="status_before_ready"
                      javaType="com.moego.server.grooming.enums.AppointmentStatusEnum"
                      typeHandler="com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"/>
      <columnOverride column="status_before_finish"
                      javaType="com.moego.server.grooming.enums.AppointmentStatusEnum"
                      typeHandler="com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler"/>
      <columnOverride column="wait_list_status"
                      javaType="com.moego.server.grooming.enums.WaitListStatusEnum"
                      typeHandler="com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler"/>
    </table> -->

    <!--    <table tableName="moe_book_online_deposit"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--      <columnOverride column="preauth_info" javaType="com.moego.server.grooming.dto.BookOnlineDepositDTO.PreAuth" typeHandler="com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler"/>-->
    <!--    </table>-->
    <!--    <table tableName="moe_grooming_package"-->
    <!--           enableCountByExample="false"-->
    <!--           enableSelectByExample="false"-->
    <!--           enableUpdateByExample="false"-->
    <!--           enableDeleteByExample="false">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--        <table tableName="moe_grooming_service_category"-->
    <!--               enableCountByExample="false"-->
    <!--               enableSelectByExample="false"-->
    <!--               enableUpdateByExample="false"-->
    <!--               enableDeleteByExample="false">-->
    <!--          <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--        </table>-->
    <!--    <table tableName="moe_grooming_service" enableCountByExample="true" enableSelectByExample="true" enableDeleteByExample="false" enableUpdateByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--      <columnOverride column="description" javaType="String" jdbcType="VARCHAR"/>-->
    <!--    </table>-->


    <!--    <table tableName="ob_config_client_review"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--    <table tableName="ob_config_team"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--    <table tableName="abandoned_schedule_message_setting"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--    <table tableName="moe_grooming_invoice_apply_package"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--    <table tableName="order_decoupling_flow_marker"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
    <!--    </table>-->

    <!--    <table tableName="appointment_outbox"-->
    <!--           enableCountByExample="false"-->
    <!--           enableSelectByExample="false"-->
    <!--           enableUpdateByExample="false"-->
    <!--           enableDeleteByExample="false">-->
    <!--        <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--        <columnOverride column="event_time" javaType="java.time.LocalDateTime"/>-->
    <!--        <columnOverride column="event_type"-->
    <!--                        javaType="com.moego.idl.models.event_bus.v1.EventType" jdbcType="INTEGER"-->
    <!--                        typeHandler="com.moego.server.grooming.mapper.typehandler.EventTypeHandler"/>-->
    <!--        <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>-->
    <!--        <columnOverride column="status"-->
    <!--                        javaType="com.moego.idl.models.appointment.v1.OutboxSendStatus" jdbcType="TINYINT"-->
    <!--                        typeHandler="com.moego.server.grooming.mapper.typehandler.OutboxSendStatusHandler"/>-->
    <!--    </table>-->

    <!--    <table tableName="moe_grooming_package_history"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--      <columnOverride column="appointment_date" javaType="String"/>-->
    <!--      <columnOverride column="activity_type"-->
    <!--                      javaType="com.moego.server.grooming.enums.PackageActivityEnum" jdbcType="TINYINT"-->
    <!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.PackageActivityEnumHandler"/>-->
    <!--    </table>-->

    <!--    <table tableName="moe_book_online_landing_page_config"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--    <table tableName="moe_questionnaire" enableCountByExample="false" enableSelectByExample="false"-->
    <!--           enableDeleteByExample="false" enableUpdateByExample="false">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--            <table tableName="moe_grooming_pet_detail">-->
    <!--              <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--              <columnOverride column="scope_type_price" javaType="Integer" jdbcType="INTEGER"/>-->
    <!--              <columnOverride column="scope_type_time" javaType="Integer" jdbcType="INTEGER"/>-->
    <!--              <columnOverride column="work_mode" javaType="Integer" jdbcType="INTEGER"/>-->
    <!--              <columnOverride column="service_type" javaType="Integer" jdbcType="INTEGER"/>-->
    <!--              <columnOverride column="service_item_type" javaType="Integer" jdbcType="INTEGER"/>-->
    <!--              <columnOverride column="duration_override_type"-->
    <!--                              javaType="com.moego.idl.models.offering.v1.ServiceOverrideType" jdbcType="TINYINT"-->
    <!--                              typeHandler="com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler"/>-->
    <!--              <columnOverride column="price_override_type"-->
    <!--                              javaType="com.moego.idl.models.offering.v1.ServiceOverrideType" jdbcType="TINYINT"-->
    <!--                              typeHandler="com.moego.server.grooming.mapper.typehandler.ServiceOverrideTypeTypeHandler"/>-->
    <!--              <ignoreColumn column="service_name"/>-->
    <!--              <ignoreColumn column="service_description"/>-->
    <!--              <ignoreColumn column="tax_id"/>-->
    <!--              <ignoreColumn column="tax_rate"/>-->
    <!--            </table>-->

    <!--    <table tableName="moe_qb_connect"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--    <table tableName="moe_qb_task"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableDeleteByExample="true"-->
    <!--           enableUpdateByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

    <!--    <table tableName="moe_qb_sync_sales_receipt"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableDeleteByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--    >-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->

        <table tableName="moe_book_online_service"
               enableCountByExample="true"
               enableSelectByExample="true"
               enableDeleteByExample="true"
               enableUpdateByExample="true">
          <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
          <columnOverride column="show_duration" javaType="Integer" jdbcType="INTEGER"/>
        </table>

    <!--    <table tableName="moe_business_book_online"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableDeleteByExample="true"-->
    <!--           enableUpdateByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--      <columnOverride column="service_areas"-->
    <!--                      javaType="java.util.List&lt;java.lang.Integer>"-->
    <!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.IntListTypeHandler"/>-->
    <!--      <columnOverride column="payment_option_map"-->
    <!--                      javaType="java.util.Map&lt;Integer, com.moego.server.grooming.dto.BookOnlineDTO.PaymentOption>"-->
    <!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBusinessBookOnlinePaymentOptionMapTypeHandler"/>-->
    <!--    </table>-->

    <!-- <table tableName="moe_grooming_repeat"
            enableCountByExample="true"
            enableSelectByExample="true"
            enableDeleteByExample="true"
            enableUpdateByExample="true">
       <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
     </table>-->
    <!--    <table tableName="moe_grooming_addon_applicable_service"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableDeleteByExample="true"-->
    <!--           enableUpdateByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--    </table>-->
    <!--        <table tableName="moe_grooming_package"-->
    <!--               enableCountByExample="true"-->
    <!--               enableSelectByExample="true"-->
    <!--               enableDeleteByExample="false"-->
    <!--               enableUpdateByExample="true">-->
    <!--          <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--        </table>-->
    <!--    <table tableName="moe_grooming_package_service"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableDeleteByExample="false"-->
    <!--           enableUpdateByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--      <columnOverride-->
    <!--        column="services"-->
    <!--        javaType="java.util.List&lt;com.moego.server.retail.dto.PackageInfoDto.Service&gt;"-->
    <!--        typeHandler="com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler"/>-->
    <!--    </table>-->
    <!--    </table>-->
    <!--          <table tableName="moe_qb_setting" enableCountByExample="true" enableSelectByExample="true"-->
    <!--               enableDeleteByExample="false" enableUpdateByExample="true">-->
    <!--            <generatedKey column="id" sqlStatement="Mysql" identity="true" />-->
    <!--            <columnOverride column="service_areas"-->
    <!--                              javaType="java.util.List&lt;java.lang.Integer>"-->
    <!--                              typeHandler="com.moego.server.grooming.mapper.typehandler.IntListTypeHandler" />-->
    <!--          </table>-->
    <!--<table tableName="moe_book_online_profile"
           enableCountByExample="true"
           enableSelectByExample="true"
           enableUpdateByExample="true"
           enableDeleteByExample="true">
      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
    </table>-->

    <!--    <table tableName="appointment_pet_medication"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--      <columnOverride column="date_type"-->
    <!--                      javaType="com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType" jdbcType="INTEGER"-->
    <!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler"/>-->
    <!--    </table>-->

    <!--  <table tableName="appointment_pet_schedule_setting"
           enableCountByExample="true"
           enableSelectByExample="true"
           enableUpdateByExample="true"
           enableDeleteByExample="true">
      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
    </table> -->

    <!--    <table tableName="moe_book_online_abandon_record"-->
    <!--           enableCountByExample="true"-->
    <!--           enableSelectByExample="true"-->
    <!--           enableUpdateByExample="true"-->
    <!--           enableDeleteByExample="true">-->
    <!--      <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--      <columnOverride column="specific_dates" javaType="java.util.List&lt;java.lang.String&gt;"-->
    <!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.StringListTypeHandler"/>-->
    <!--    </table>-->

<!--    <table tableName="moe_book_online_question"-->
<!--           enableCountByExample="true"-->
<!--           enableSelectByExample="true"-->
<!--           enableUpdateByExample="true"-->
<!--           enableDeleteByExample="true">-->
<!--      <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--      <ignoreColumn column="accept_pet_entry_type"/>-->
<!--      <ignoreColumn column="accepted_customer_type"/>-->
<!--      <columnOverride column="new_pet_access_mode"-->
<!--                      javaType="com.moego.idl.models.online_booking.v1.NewPetAccessMode"-->
<!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler"/>-->
<!--      <columnOverride column="existing_pet_access_mode"-->
<!--                      javaType="com.moego.idl.models.online_booking.v1.ExistingPetAccessMode"-->
<!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler"/>-->
<!--      <columnOverride column="new_client_access_mode"-->
<!--                      javaType="com.moego.idl.models.online_booking.v1.NewClientAccessMode"-->
<!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler"/>-->
<!--      <columnOverride column="existing_client_access_mode"-->
<!--                      javaType="com.moego.idl.models.online_booking.v1.ExistingClientAccessMode"-->
<!--                      typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler"/>-->
<!--    </table>-->

    <!--        <table tableName="moe_book_online_abandon_record_event_log"-->
    <!--               enableCountByExample="true"-->
    <!--               enableSelectByExample="true"-->
    <!--               enableUpdateByExample="true"-->
    <!--               enableDeleteByExample="true">-->
    <!--          <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
    <!--        </table>-->
  </context>
</generatorConfiguration>
